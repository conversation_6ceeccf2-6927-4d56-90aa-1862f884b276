import { LayoutElementContentItemType, LayoutElementContentType } from '../../../definitions';

export type LayoutElementContentTypeKeys = keyof typeof LayoutElementContentType;

export interface LayoutElementContentTypeSpec {
  label: string;
  /**
   * This is also the property name that is used for the layout element config to hold the data.
   * It is not an array, as the API can only provide one selected* property to be filled.
   * We need to manually keep it in sync with the TypeScript definitions.
   * So if you define for example selectedArticles for a layout item definition, you should keep it in sync with this property.
   * This is also used for the drag'n'drop and other content related checks.
   */
  itemContentType?: LayoutElementContentItemType;
  isConfigurable?: boolean;
  isAutoFillQuickAction?: boolean;
  isOverrideable?: boolean;
  hasConfigurableBlockTitle?: boolean;
  /**
   * This is used to indicate that the content type is a secondary content type.
   * In this case this can override the default content type.
   */
  isSecondaryContentTypeForce?: boolean;
}

export const LayoutElementContentTypeSpecs: Record<LayoutElementContentType, LayoutElementContentTypeSpec> = {
  [LayoutElementContentType.Article]: {
    label: 'Cikk',
    itemContentType: LayoutElementContentItemType.ARTICLES,
    isConfigurable: true,
    isAutoFillQuickAction: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.Ad]: {
    label: 'Hirdetés',
    isConfigurable: false,
    isAutoFillQuickAction: false,
    isOverrideable: false,
  },
  [LayoutElementContentType.StockChart]: {
    label: 'Tőzsde',
    isConfigurable: false,
  },
  [LayoutElementContentType.Opinion]: {
    label: 'Vélemény',
    itemContentType: LayoutElementContentItemType.OPINIONS,
    isConfigurable: true,
    isAutoFillQuickAction: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.OpinionList]: {
    label: 'Vélemény lista',
    isConfigurable: false,
  },
  [LayoutElementContentType.OpinionBlock]: {
    label: 'Vélemény blokk',
    isConfigurable: true,
    isAutoFillQuickAction: true,
    isOverrideable: false,
  },
  [LayoutElementContentType.INGATLANBAZAR]: {
    label: 'Ingatlanbazár',
    isConfigurable: false,
  },
  [LayoutElementContentType.Video]: {
    label: 'Videó',
    isConfigurable: true,
    isAutoFillQuickAction: true,
    isOverrideable: true,
    itemContentType: LayoutElementContentItemType.VIDEOS,
  },
  [LayoutElementContentType.Dossier]: {
    label: 'Dosszié',
    isConfigurable: true,
    isAutoFillQuickAction: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.PodcastBlock]: {
    label: 'Podcast blokk',
    isConfigurable: true,
    isAutoFillQuickAction: false,
    isOverrideable: false,
  },
  [LayoutElementContentType.VideoBlock]: {
    label: 'Videó blokk',
    isConfigurable: true,
    hasConfigurableBlockTitle: true,
  },
  [LayoutElementContentType.NewsletterBlock]: {
    label: 'Hírlevél blokk',
    isConfigurable: false,
  },
  [LayoutElementContentType.FreshBlock]: {
    label: 'Friss blokk',
    isConfigurable: true,
    isOverrideable: false,
  },
  [LayoutElementContentType.FreshNews]: {
    label: 'Friss hírek',
    isConfigurable: true,
  },
  [LayoutElementContentType.FastNews]: {
    label: 'Gyors hír',
    isConfigurable: true,
    isOverrideable: true,
    isAutoFillQuickAction: true,
  },
  [LayoutElementContentType.LinkList]: {
    label: 'Link lista',
    isConfigurable: true,
  },
  [LayoutElementContentType.Vote]: {
    label: 'Szavazás',
    isConfigurable: true,
    itemContentType: LayoutElementContentItemType.VOTE,
  },
  [LayoutElementContentType.SPONSORED_VOTE]: {
    label: 'Szponzorált szavazás',
    isConfigurable: true,
    itemContentType: LayoutElementContentItemType.VOTE,
  },
  [LayoutElementContentType.MULTI_VOTE]: {
    label: 'Multi-Szavazás',
    isConfigurable: true,
    itemContentType: LayoutElementContentItemType.MULTI_VOTE,
  },
  [LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION]: {
    label: 'Élmény alkalom',
    isConfigurable: true,
    itemContentType: LayoutElementContentItemType.OCCASION,
  },
  [LayoutElementContentType.Gallery]: {
    label: 'Galéria',
    isConfigurable: true,
  },
  [LayoutElementContentType.BrandingBox]: {
    label: 'Branding Box',
    itemContentType: LayoutElementContentItemType.BRANDING,
    isConfigurable: true,
  },
  [LayoutElementContentType.VisegradPost]: {
    label: 'Visegrád poszt',
    isConfigurable: true,
  },
  [LayoutElementContentType.CultureNation]: {
    label: 'Kultúrnemzet',
    isConfigurable: false,
  },
  [LayoutElementContentType.HtmlEmbed]: {
    label: 'HTML beágyazás',
    isConfigurable: true,
  },
  [LayoutElementContentType.TabsBlock]: {
    label: 'Tabos blokk',
    isConfigurable: true,
  },
  [LayoutElementContentType.Breaking]: {
    label: 'Breaking',
    itemContentType: LayoutElementContentItemType.BREAKING,
    isConfigurable: true,
  },
  [LayoutElementContentType.Note]: {
    label: 'Jegyzet',
    isConfigurable: true,
    isAutoFillQuickAction: true,
    itemContentType: LayoutElementContentItemType.NOTES,
    isOverrideable: true,
  },
  [LayoutElementContentType.Wysiwyg]: {
    label: 'Szöveges tartalom',
    itemContentType: LayoutElementContentItemType.WYSIWYG,
    isConfigurable: true,
  },
  [LayoutElementContentType.Image]: {
    label: 'Kép',
    itemContentType: LayoutElementContentItemType.IMAGE,
    isConfigurable: true,
  },
  [LayoutElementContentType.TENYEK_BOX]: {
    label: 'Tények doboz',
    isConfigurable: false,
  },
  [LayoutElementContentType.Astrology]: {
    label: 'Asztrológia',
  },
  [LayoutElementContentType.Tabs]: {
    label: 'Tabok',
    isConfigurable: true,
  },
  [LayoutElementContentType.PrBlock]: {
    label: 'PR blokk',
    isConfigurable: true,
    isOverrideable: true,
    isAutoFillQuickAction: true,
  },
  [LayoutElementContentType.Quiz]: {
    label: 'Kvíz',
    isConfigurable: true,
  },
  [LayoutElementContentType.SponsoredQuiz]: {
    label: 'Szponzorált Kvíz',
    isConfigurable: true,
  },
  [LayoutElementContentType.TrendingTagsBlock]: {
    label: 'Trending címkék',
    isConfigurable: true,
  },
  [LayoutElementContentType.TagBlock]: {
    label: 'Címke szalag',
    isConfigurable: true,
  },
  [LayoutElementContentType.KompostBlock]: {
    label: 'Kompost',
    isConfigurable: true,
    isAutoFillQuickAction: false,
    isSecondaryContentTypeForce: true,
  },
  [LayoutElementContentType.YessfactorBlock]: {
    label: 'Yessfactor',
    isConfigurable: true,
    isAutoFillQuickAction: false,
    isSecondaryContentTypeForce: true,
  },
  [LayoutElementContentType.BrownRecommender]: {
    label: 'Barna ajánló',
    isConfigurable: true,
  },
  [LayoutElementContentType.BestRecommender]: {
    label: 'Metropol Best ajánló',
    isConfigurable: true,
  },
  [LayoutElementContentType.FinalCountdown]: {
    label: 'Visszaszámláló',
    isConfigurable: true,
  },
  [LayoutElementContentType.HelloBudapest]: {
    label: 'Hello Budapest',
    isConfigurable: true,
  },
  [LayoutElementContentType.koponyeg]: {
    label: 'Köpönyeg',
    isConfigurable: false,
  },
  [LayoutElementContentType.IngatlanbazarSearch]: {
    label: 'Ingatalanbazár kereső box',
    isConfigurable: true,
  },
  [LayoutElementContentType.IngatlanbazarConfigurable]: {
    label: 'Ingatalanbazár (konfigurálható)',
    isConfigurable: false,
  },
  [LayoutElementContentType.DossierList]: {
    label: 'Dosszié lista',
    isConfigurable: true,
  },
  [LayoutElementContentType.BrandingBoxEx]: {
    label: 'Branding Box Külső forrásból',
    isConfigurable: false,
  },
  [LayoutElementContentType.CategoryStepper]: {
    label: 'Rovat blokk',
    isConfigurable: true,
    isAutoFillQuickAction: true,
  },
  [LayoutElementContentType.WeeklyNewspaperBox]: {
    label: 'Hetilap box',
    isConfigurable: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT]: {
    label: 'Videós cikkek',
    isConfigurable: true,
    hasConfigurableBlockTitle: true,
  },
  [LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT]: {
    label: 'Podcastos cikk blokk',
    hasConfigurableBlockTitle: true,
    isConfigurable: true,
    isOverrideable: false,
  },
  [LayoutElementContentType.PODCAST_LIST]: {
    label: 'Podcast lista',
    isConfigurable: true,
    itemContentType: LayoutElementContentItemType.PODCASTS,
  },
  [LayoutElementContentType.PdfBox]: {
    label: 'PDF doboz',
    isConfigurable: true,
  },
  [LayoutElementContentType.DossierRepeater]: {
    label: 'Dossziék',
    isConfigurable: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.MANUAL_OPINION]: {
    label: 'Manuális Vélemény',
    isConfigurable: false,
  },
  [LayoutElementContentType.MANUAL_ARTICLE]: {
    label: 'Manuális Cikk',
    isConfigurable: false,
  },
  [LayoutElementContentType.RSS_BOX]: {
    label: 'RSS doboz',
    isConfigurable: false,
  },
  [LayoutElementContentType.MAP_RECOMMENDATIONS]: {
    label: 'Térképajánló',
    isConfigurable: false,
  },
  [LayoutElementContentType.HERO]: {
    label: 'Hero',
    isConfigurable: true,
  },
  [LayoutElementContentType.MEDICAL_METEOROLOGY]: {
    label: 'Orvosmeteorológia',
    isConfigurable: false,
  },
  [LayoutElementContentType.TWELVE_DAYS_FORECAST]: {
    label: '12 napos előrejelzés',
    isConfigurable: false,
  },
  [LayoutElementContentType.NEWSPAPER]: {
    label: 'Újság előfizetés',
    isConfigurable: true,
  },
  [LayoutElementContentType.DETECTIONS]: {
    label: 'Észlelések',
    isConfigurable: true,
  },
  [LayoutElementContentType.NEWS_FEED]: {
    label: 'Hírfolyam',
    isConfigurable: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.DATA_BANK]: {
    label: 'Adatbank',
    isConfigurable: true,
  },
  [LayoutElementContentType.SPORT_RADIO_PLAYER]: {
    label: 'Sport Rádió lejátszó',
    isConfigurable: false,
  },
  [LayoutElementContentType.LEAD_EDITORS]: {
    label: 'Vezető szerkesztők',
    isConfigurable: true,
  },
  [LayoutElementContentType.TEAMS]: {
    label: 'Csapatok',
    isConfigurable: true,
  },
  [LayoutElementContentType.TRIP_BOX]: {
    label: 'Forduló box',
    isConfigurable: true,
  },
  [LayoutElementContentType.UPCOMING_MATCHES]: {
    label: 'Közelgő mérkőzések',
    isConfigurable: true,
  },
  [LayoutElementContentType.CHAMPIONSHIP_TABLE]: {
    label: 'Bajnokság Tabella',
    isConfigurable: true,
  },
  [LayoutElementContentType.SOCIAL_MEDIA]: {
    label: 'Social Media box',
    isConfigurable: false,
  },
  [LayoutElementContentType.MEDIA_PANEL]: {
    label: 'Kiemelt box',
    isConfigurable: true,
  },
  [LayoutElementContentType.MOST_VIEWED]: {
    label: 'Legolvasottabb',
    isConfigurable: true,
  },
  [LayoutElementContentType.IMAGE_MAP_LIST]: {
    label: 'Képlistás térképek',
    isConfigurable: false,
  },
  [LayoutElementContentType.DRAWN_MAP_LIST]: {
    label: 'Rajzolt térképek',
    isConfigurable: false,
  },
  [LayoutElementContentType.TOP_STORIES]: {
    label: 'Top Stories',
    isConfigurable: false,
  },
  [LayoutElementContentType.GP_NEWS_BOX]: {
    label: 'GP Hírek doboz',
    isConfigurable: true,
  },
  [LayoutElementContentType.SPORT_BLOCK]: {
    label: 'Origo Sport doboz',
    isConfigurable: true,
  },
  [LayoutElementContentType.MORE_ARTICLES]: {
    label: 'További cikkek link',
    isConfigurable: true,
  },
  [LayoutElementContentType.RELATED_ARTICLES]: {
    label: 'Kapcsolódó cikkek',
    isConfigurable: true,
  },
  [LayoutElementContentType.RECIPE_CATEGORY_SELECT]: {
    label: 'Recept kategória választó',
    isConfigurable: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.WEEKLY_MENU]: {
    label: 'Heti menü',
    isConfigurable: false,
  },
  [LayoutElementContentType.MAESTRO_BOX]: {
    label: 'Maestro doboz',
    isConfigurable: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.TEXT_BOX]: {
    label: 'Szöveges doboz',
    isConfigurable: true,
  },
  [LayoutElementContentType.ARTICLE_SLIDER]: {
    label: 'Lapozható cikk',
    isConfigurable: true,
  },
  [LayoutElementContentType.TURPI_BOX]: {
    label: 'Turpi doboz',
    isConfigurable: true,
  },
  [LayoutElementContentType.TURPI_CARD]: {
    label: 'Turpi kártya',
    isConfigurable: true,
  },
  [LayoutElementContentType.AUTHOR]: {
    label: 'Szerző',
    isConfigurable: true,
  },
  [LayoutElementContentType.INGREDIENT]: {
    label: 'Hozzávaló',
    isConfigurable: true,
  },
  [LayoutElementContentType.RECIPE]: {
    label: 'Recept kártya',
    isConfigurable: true,
    isAutoFillQuickAction: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.RECIPE_SWIPER]: {
    label: 'Lapozható recept',
    isConfigurable: true,
  },
  [LayoutElementContentType.LIVE_BAR]: {
    label: 'Élő közvetítés',
  },
  [LayoutElementContentType.GUARANTEE_BOX]: {
    label: 'Garancia ajánló',
    isConfigurable: true,
  },
  [LayoutElementContentType.Kulturnemzet]: {
    label: 'Kultúrnemzet',
    isConfigurable: false,
  },
  [LayoutElementContentType.Sorozatveto]: {
    label: 'Sorozatvető',
    isConfigurable: true,
  },
  [LayoutElementContentType.OFFER_BOX]: {
    label: 'Ajánlatok doboz',
    isConfigurable: true,
  },
  [LayoutElementContentType.MinuteToMinute]: {
    label: 'Percről percre',
    isConfigurable: true,
    hasConfigurableBlockTitle: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.ARTICLE_BLOCK]: {
    label: 'Cikk blokk',
    isConfigurable: true,
  },
  [LayoutElementContentType.GALLERY_ARTICLE_LIST]: {
    label: 'Galériás cikk lista',
    isConfigurable: true,
    hasConfigurableBlockTitle: false,
  },
  [LayoutElementContentType.LATEST_NEWS]: {
    label: 'Friss hírek',
    isConfigurable: true,
  },
  [LayoutElementContentType.BLOCK_SEPARATOR]: {
    label: 'Elválasztó vonal',
    isConfigurable: false,
  },
  [LayoutElementContentType.HIGHLIGHTED_SELECTION]: {
    label: 'Válogatás',
    isConfigurable: true,
  },
  [LayoutElementContentType.BRANDING_BOX_ARTICLE]: {
    label: 'Branding box cikk lista',
    isConfigurable: true,
  },
  [LayoutElementContentType.BLOG]: {
    label: 'Blog',
    isConfigurable: true,
    isAutoFillQuickAction: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.DAILY_MENU]: {
    label: 'Napi menü',
    isConfigurable: false,
  },
  [LayoutElementContentType.DID_YOU_KNOW]: {
    label: 'Változó tartalmú branding box',
    isConfigurable: true,
  },
  [LayoutElementContentType.CONFERENCE]: {
    label: 'Konferencia',
    isConfigurable: true,
  },
  [LayoutElementContentType.TELEKOM_VIVICITTA]: {
    label: 'Telekom Vivicittá',
    isConfigurable: false,
  },
  [LayoutElementContentType.AGROKEP]: {
    label: 'Agrokép',
    isConfigurable: false,
  },
  [LayoutElementContentType.AGROKEP_LIST]: {
    label: 'Agrokép lista',
    isConfigurable: false,
  },
  [LayoutElementContentType.NewsletterBlockGong]: {
    label: 'Hírlevél Gong',
    isConfigurable: false,
  },
  [LayoutElementContentType.SELECTION]: {
    label: 'Válogatás',
    isConfigurable: true,
    isOverrideable: true,
  },
  [LayoutElementContentType.VISITOR_COUNTER]: {
    label: 'Látogató számláló',
    isConfigurable: false,
  },
  [LayoutElementContentType.PROGRAM]: {
    label: 'Program',
    itemContentType: LayoutElementContentItemType.PROGRAM,
    isConfigurable: true,
  },
  [LayoutElementContentType.DAILY_PROGRAM]: {
    label: 'Napi program',
    isConfigurable: true,
  },
  [LayoutElementContentType.COUNTDOWN_BOX]: {
    label: 'Visszaszámláló doboz',
    isConfigurable: true,
  },
  [LayoutElementContentType.EB_SINGLE_ELIMINATION]: {
    label: 'EB egyenes kiesés',
    isConfigurable: true,
  },
  [LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE]: {
    label: 'EB visszaszámláló blokk cím',
    isConfigurable: true,
  },
  [LayoutElementContentType.ELECTIONS_BOX]: {
    label: 'Választások doboz',
    isConfigurable: true,
  },
  [LayoutElementContentType.OLIMPIA_COUNTDOWN_BLOCK_TITLE]: {
    label: 'Olimpia visszaszámláló blokk cím',
    isConfigurable: true,
  },
  [LayoutElementContentType.EB_NEWS]: {
    label: 'EB - Hírek',
    isConfigurable: false,
  },
  [LayoutElementContentType.OLIMPIA_NEWS]: {
    label: 'Olimpia - Hírek',
    isConfigurable: true,
  },
  [LayoutElementContentType.OLIMPIA_HUNGARIAN_TEAM]: {
    label: 'Olimpia - Magyar csapat',
    isConfigurable: true,
  },
  [LayoutElementContentType.OLIMPIA_ARTICLES_WITH_PODCAST_CONTENT]: {
    label: 'Olimpia - podcast blokk',
    isConfigurable: true,
  },
  [LayoutElementContentType.OLIMPIA_LARGE_NAVIGATOR]: {
    label: 'Olimpia - nagy terelő',
    isConfigurable: false,
  },
  [LayoutElementContentType.BAYER_BLOG]: {
    label: 'Bayer Zsolt Blogja',
    itemContentType: LayoutElementContentItemType.OPINIONS,
    isConfigurable: true,
  },
  [LayoutElementContentType.WHERE_THE_BALL_WILL_BE]: {
    label: 'Ahol a labda lesz',
    itemContentType: LayoutElementContentItemType.OPINIONS,
    isConfigurable: true,
  },
  [LayoutElementContentType.PUBLIC_AUTHORS]: {
    label: 'Nyilvános szerzők',
    isConfigurable: true,
  },
  [LayoutElementContentType.SPOTLIGHT]: {
    label: 'Spotlight',
    isConfigurable: true,
  },
  [LayoutElementContentType.APP_DOWNLOAD]: {
    label: 'Alkalmazás letöltés box',
    isConfigurable: true,
  },
  [LayoutElementContentType.SPONSORED_ARTICLE_BOX]: {
    label: 'Szponzorált cikk doboz',
    isConfigurable: true,
  },
  [LayoutElementContentType.PODCAST_ARTICLE_LIST]: {
    label: 'Podcastos cikk lista',
    isConfigurable: true,
  },
  [LayoutElementContentType.SERVICES_BOX]: {
    label: 'Szolgáltatások',
    isConfigurable: true,
  },
  [LayoutElementContentType.OPINION_NEWSLETTER]: {
    label: 'Heti vélemény hírlevél',
    isConfigurable: false,
  },
  [LayoutElementContentType.PODCAST_APP_RECOMMENDER]: {
    label: 'Podcast alkalmazás terelő',
    isConfigurable: false,
  },
  [LayoutElementContentType.RIPOST7_BLOCK]: {
    label: 'Ripost7 blokk',
    isConfigurable: true,
    isAutoFillQuickAction: false,
    isSecondaryContentTypeForce: true,
  },
  [LayoutElementContentType.ASTRONET_JOSLAS]: {
    label: 'Astronet jóslás',
    isConfigurable: false,
  },
  [LayoutElementContentType.ASTRONET_CIKKEK]: {
    label: 'Astronet cikkek',
    isConfigurable: false,
  },
  [LayoutElementContentType.ASTRONET_HOROSZKOP]: {
    label: 'Astronet horoszkóp',
    isConfigurable: false,
  },
  [LayoutElementContentType.EXPERIENCE_GIFT]: {
    label: 'Élményt ajándékba',
    isConfigurable: false,
  },
  [LayoutElementContentType.EVENT_CALENDAR]: {
    label: 'Eseménynaptár',
    isConfigurable: false,
  },
  [LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION]: {
    label: 'Élmény ajánló',
    isConfigurable: true,
  },
  [LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER]: {
    label: 'Nagyképes élmény ajánló',
    isConfigurable: true,
  },
  [LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER]: {
    label: 'Élmény tematikus ajánló',
    isConfigurable: true,
  },
  [LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER]: {
    label: 'Lapozható élmény alkalom',
    isConfigurable: true,
  },
  [LayoutElementContentType.TOP_RANKING_GLOSSARY]: {
    label: 'Toplista',
    isConfigurable: false,
  },
  [LayoutElementContentType.WRITE_TO_US]: {
    label: 'Írjon nekünk',
    isConfigurable: false,
  },
  [LayoutElementContentType.JOB_LISTINGS]: {
    label: 'Álláshirdetések',
    isConfigurable: false,
  },
  [LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX]: {
    label: 'Képes szövegblokk',
    isConfigurable: true,
  },
  [LayoutElementContentType.TOP_TEN_TAGS]: {
    label: 'TOP 10 Címke',
    isConfigurable: true,
  },
  [LayoutElementContentType.STAR_BIRTHS]: {
    label: 'Ezen a napon született sztárok',
    isConfigurable: true,
  },
  [LayoutElementContentType.COLUMN_BLOCK]: {
    label: 'Rovat blokk',
    isConfigurable: true,
  },
  [LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES]: {
    label: 'Legfrissebb és a legolvasottabb cikkek',
    isConfigurable: false,
  },
  [LayoutElementContentType.SUB_COLUMNS]: {
    label: 'Alrovatok',
    isConfigurable: true,
  },
  [LayoutElementContentType.ASTRONET_BRANDING_BOX]: {
    label: 'Astronet Branding Box',
    isConfigurable: true,
  },
  [LayoutElementContentType.ASTRONET_COLUMNS]: {
    label: 'Astronet Horoszkóp rovatok',
    isConfigurable: true,
  },
  [LayoutElementContentType.SECRET_DAYS_CALENDAR]: {
    label: 'Kalendárium',
    isConfigurable: true,
  },
};
