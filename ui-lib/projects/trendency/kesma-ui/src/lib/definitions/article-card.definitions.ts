import { Params } from '@angular/router';
import { BackendDate } from '@trendency/kesma-core';
import { VideoComponentObject } from '../components/article-video/article-video.definitions';
import { ApiResponseMetaList, ApiResult, LimitableQuery, Meta } from './api-result';
import { FakeBool } from './common.definitions';
import { InitResponseBreakingNews } from './init.definitions';
import { Quiz } from './quiz.definitions';
import { BackendVoteData } from './voting.definitions';
import { BlockTitle } from './layout.definitions';
import { FocusPointUrlWithAspectRatio } from './focus-point.definitions';
import { PriorityContentFlags } from './seo.definitions';
import { Promotion } from './promotion.definitions';

export enum ArticleCardTypes {
  DEFAULT = 1,
  VIDEO,
  INSIDE_TAG,
  HORIZONAL_VIDEO,
}

export enum ContentType {
  VIDEO = 'video',
  ARTICLE = 'article',
}

export enum PodcastType {
  BRUTTO = 'brutto',
  ARBITRAZS = 'arbitrazs',
  OPERENCIA = 'operencia',
}

export type Author = Readonly<{
  name: string;
  avatarUrl?: string;
  avatar?: string;
  rank?: string;
  slug?: string;
}>;

export interface BackendPublicAuthor {
  fullName: string;
  title?: string;
  id: string;
  first_name: string;
  last_name: string;
  nick_name?: string;
  slug: string;
  public_articles?: number;
  is_active: FakeBool;
  is_inner: FakeBool;
  is_opinion_author?: FakeBool;
  rank?: string;
  introduction?: string;
  instagram?: string;
  tiktok?: string;
  facebook?: string;
  is_maestro_author?: FakeBool;
  pinterest?: string;
  youtube?: string;
  maestro_tip?: string;
  favorite_foods?: string;
  avatarFullSizeUrl: string;
}

export type ThumbnailImage = Readonly<{
  url: string;
  url43AspectRatio?: string;
  urlHuge?: string;
  alt?: string;
  fullSizeUrlHugeNative?: string;
}>;

export interface ThumbnailInfo {
  altText?: string;
  caption?: string;
  photographer?: string;
  source?: string;
  title?: string;
}

export type Label = Readonly<{
  text: string;
  url?: string | string[];
}>;

export type Region = Readonly<{
  id?: string;
  slug?: string;
  title?: string;
  shortTitle?: string;
}>;

export type Tag = Readonly<{
  id?: string;
  title?: string;
  slug: string;
  name?: string;
  color?: string;
  headerColor?: string;
  titleColor?: string;
  description?: string;
  path?: string;
  relatedThematicTags?: Omit<Tag, 'relatedThematicTags'>[];
}>;

export type SponsoredTag = Readonly<{
  tagSlug?: string | null;
  slogan?: string | null;
  fontColor?: string | null;
  backgroundColor?: string | null;
  url?: string | null;
  logoUrl?: string | null;
  logoWidth?: string | null;
  logoHeight?: string | null;
}>;

export type ArticleCategory = Readonly<{
  slug?: string;
  name?: string;
  color?: string;
}>;

export type ArticleCategoryParams = LimitableQuery &
  Readonly<{
    columnSlug?: string;
    columnId?: string;
    rowCount_limit?: string;
    page_limit?: string;
    'excludedArticleIds[]'?: string[];
    'content_types[]'?: string[];
    'columnSlugs[]'?: string[];
    fromDate?: string;
    toDate?: string;
    year?: string;
    month?: string;
  }>;

export type RegionParams = Readonly<{
  regionSlug?: string;
  regionId?: string;
  rowCount_limit?: string;
  page_limit?: string;
  'excludedArticleIds[]'?: string[];
  year?: string;
  month?: string;
  rowFrom_limit?: string;
}>;

export interface ArticleRouteParams extends Params {
  categorySlug: string;
  year?: string;
  month?: string;
  articleSlug: string;
  previewHash?: string;
  previewType?: 'accepted';
}

export interface SearchQuery extends Record<string, string | undefined> {
  global_filter?: string;
  author?: string;
  authorSlug?: string;
  from_date?: string;
  to_date?: string;
  column?: string;
  exclude_articles?: string;
  isFoundationContent?: string;
}

export type ArticleSearchResult = ArticleSocial &
  Readonly<{
    id?: string;
    contentType: string;
    title: string;
    lead: string;
    length: number;
    publishDate: string | Date;
    slug: string;
    columnSlug: string;
    columnTitle: string;
    columnTitleColor: string;
    author: string;
    authorSlug?: string;
    image?: string;
    thumbnail?: string;
    tag?: Tag;
    tags?: Tag[];
    regions?: Region[];
    preTitle: string;
    year: string;
    month: string;
    isPaywalled?: boolean | FakeBool;
    isVideo?: boolean | FakeBool;
    isAdultsOnly?: boolean;
    hasGallery?: boolean | FakeBool;
    hasDossiers?: boolean;
    hideThumbnailFromBody?: boolean;
    isProtectedContent?: boolean;
    firstTagId?: string;
    publicAuthorM2M?: PublicMultiAuthor[];
    minuteToMinute?: MinuteToMinuteState;
    foundationTagSlug?: string;
    foundationTagTitle?: string;
    podcastGuestName?: string;
    podcastGuestNameTitle?: string;
    columnEmphasizeOnArticleCard?: boolean | FakeBool;
    isPodcastType?: boolean | FakeBool;
    isGuaranteeType?: FakeBool;
    thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
    brand?: string;
    isVidcast?: FakeBool;
    columnMainColor?: string;
  }>;

export type BackendArticleSearchResult = ArticleSocial &
  Readonly<{
    id?: string;
    contentType?: ContentType;
    title: string;
    lead: string;
    length: string;
    publishDate: string;
    firstPublishDate?: string;
    slug: string;
    columnSlug: string;
    columnTitle: string;
    author: string;
    authorSlug?: string;
    avatarImage?: string;
    avatar?: string;
    image?: string;
    thumbnail?: string;
    tag?: Tag;
    tags?: Tag[];
    regions?: Region[];
    preTitle: string;
    isVideo?: boolean;
    firstRegionId?: string;
    firstTagId?: string;
    isPodcastType: boolean;
    isVideoType: boolean;
    isAdultsOnly: boolean;
    hasGallery?: boolean;
    hasDossiers?: boolean;
    articleMedium?: string;
    chLead?: string;
    chExcerpt?: string;
    excerpt?: string;
    isPaywalled?: boolean;
    hideThumbnailFromBody?: string | boolean;
    isProtectedContent?: boolean;
    publicAuthor?: string;
    publicAuthorEmail?: string;
    publicAuthorTitle?: string;
    publicAuthorDescription?: string;
    publicAuthorSlug?: string;
    publicAuthorM2M?: PublicMultiAuthor[];
    foundationTagSlug?: string;
    foundationTagTitle?: string;
    columnEmphasizeOnArticleCard?: boolean;
    isGuaranteeType?: FakeBool;
    thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
    avatarImageFocusedImages?: FocusPointUrlWithAspectRatio;
    brand?: string;
  }>;

export type GameDetails = Readonly<{
  description: string;
  id?: string;
  url?: string;
  game?: Game;
}>;

export type Game = Readonly<{
  backgroundColor: string;
  logo: string;
  title: string;
  blocks?: GameDetails[];
}>;

export type ArticleColumn = Readonly<{
  id: string;
  title: string;
  titleColor?: string; // Text color
  mainColor?: string; // Background color
  slug: string;
  seoTitle?: string;
  seoLead?: string;
  thumbnail?: string; // url
  sponsorship?: string;
  parent?: ArticleColumn;
  columnEmphasizeOnArticleCard?: boolean;
}>;

export enum ArticleBodyType {
  Wysywyg = 'Basic.Wysiwyg.Wysiwyg',
  Article = 'ContentPage.Article',
  Voting = 'Voting.Voting',
  MediaVideo = 'Media.Video.Video',
  Gallery = 'Media.Gallery.Gallery',
  SubsequentDossier = 'Subsequent.Dossier.Dossier',
  Quiz = 'ContentGroup.Quiz',
  Advert = 'Eadvert.Eadvert',
  Game = 'Game.GameBlock',
  DoubleArticleRecommendation = 'DoubleArticleRecommendationOptional.Double',
  SubsequentDossierNewsFeed = 'Subsequent.DossierNewsFeed.DossierNewsFeed',
  Rating = 'Rating.Rating',
  NewsletterSignUp = 'NewsletterSignUp.NewsletterSignUp',
  ExternalArticleRecommendation = 'Recommendation.ExternalRecommendation',
  Recipe = 'Recipe.Recipe',
  Infobox = 'InfoBox.InfoBox',
  GroupTable = 'Sport.GroupTabella',
  Table = 'Sport.Tabella',
  RaceWeekend = 'Sport.RaceWeekend',
  MotorsportWorldCup = 'Sport.MotorsportWorldCup',
  Schedule = 'Sport.Schedule',
  CompetitionTeam = 'Sport.CompetitionTeam',
  TenArticleRecommender = 'TenArticleRcm.TenArticle',
  TenOpinionRecommender = 'TenOpinionArticleRcm.TenOpinionArticle',
  Pagination = 'Pagination.Pagination',
  NewsletterGongSignUp = 'NewsletterGongSignup.NewsletterGongSignup',
  Stock = 'StockMarketCurrency.StockMarketCurrency',
  TippmixAdvert = 'TippmixAdvert.TippmixAdvert',
  DoubleArticleRecommendations = 'DoubleArticleRecommendation.Double',
  TwoArticle = 'ContentPage.TwoArticle',
  TenArticle = 'TenArticleRcmVg.TenArticle',
  SportCompetitionSchedules = 'Sport.CompetitionSchedules',
  AutoMotor = 'ShortCode.AutoMotor.AutoMotor',
  SportSingleElimination = 'Sport.CompetitionStraightElimination',
  SportDailySchedules = 'Sport.DailySchedules',
  SportCompetitionPhaseStat = 'Sport.CompetitionPhaseStat',
  MultiVoting = 'MultiVote.MultiVote',
  Promotion = 'Promotion.Promotion',
  AnchorToComments = 'ShortCode.AnchorToComments.Item',
}

export enum ArticleBodyDetailType {
  WysywygDetail = 'Basic.Wysiwyg.WysiwygDetail',
  SubsequentDossierDetail = 'Subsequent.Dossier.DossierDetail',
  MediaVideoDetail = 'Media.Video.VideoDetail',
  QuizDetail = 'ContentGroup.QuizDetail',
  GalleryDetail = 'Media.Gallery.GalleryDetail',
  AdvertDetail = 'Eadvert.EadvertDetail',
  InfoBoxTitle = 'InfoBox.InfoBoxTitle',
  InfoBoxDescription = 'InfoBox.InfoBoxDescription',
  InfoBoxType = 'InfoBox.InfoBoxType',
  TippmixAdvertDetail = 'TippmixAdvertDetail',
  PromotionDetail = 'Promotion.PromotionDetail',
}

export interface ArticleBodyDetails {
  type: ArticleBodyDetailType;
  key: string;
  // TODO: Nem szép itt az any, viszont a string önmagában félrevezető, mert ezer féle dolgot jöhet innen:
  value: any | string | BackendVoteData;
}

export interface ArticleBodyInfoBoxDetails {
  type: ArticleBodyDetailType;
  key: string;
  value:
    | string
    | {
        title: string;
        value: string;
      };
}

export interface ArticleBodyInfoBoxElementData extends ArticleBodyDetails {
  type: ArticleBodyDetailType.InfoBoxTitle | ArticleBodyDetailType.InfoBoxDescription | ArticleBodyDetailType.InfoBoxType;
  details: ArticleBodyInfoBoxDetails[];
}

export type GalleryElementData = Readonly<{
  type: ArticleBodyType.Gallery;
  id: string;
  details: GalleryElementDetail[];
  subComponents: ComponentData[];
}>;

export type GalleryElementDetail = Readonly<{
  type: ArticleBodyDetailType.GalleryDetail;
  key: 'gallery';
  value: {
    id: string;
    slug: string;
    title: string;
    highlightedImage: {
      thumbnailUrl: string;
      photographer: null;
    };
  };
}>;

export type TippmixAdvertDetail = Readonly<{
  type: ArticleBodyDetailType.TippmixAdvertDetail;
  key: string;
  value: {
    advertText?: string;
    link?: string;
    linkText?: string;
    title?: string;
  };
}>;

export type QuizElementData = Readonly<{
  type: ArticleBodyType.Quiz;
  id: string;
  details: QuizElementDetail[];
  subComponents: ComponentData[];
}>;

export type QuizElementDetail = Readonly<{
  type: ArticleBodyDetailType.QuizDetail;
  key: 'quiz';
  value: Quiz;
}>;

export type ArticleBody = Readonly<{
  type: ArticleBodyType;
  id: string;
  details: ArticleBodyDetails[];
  subComponents: ComponentData[];
}>;

export type ArticleLanguage = Readonly<{
  columnTitle: string;
  columnSlug: string;
  languageTitle: string;
  languageSlug: string;
  articleSlug: string;
  articlePublishDate: string;
}>;

export type Article = Readonly<{
  id: string;
  title: string;
  lead: string;
  publishDate?: Date;
  rawPublishDate?: string;
  lastUpdated?: Date;
  rawLastUpdated?: string;
  publicAuthor?: string;
  publicAuthorEmail?: string;
  publicAuthorTitle?: string;
  publicAuthorDescription?: string;
  publicAuthorSlug?: string;
  publicAuthorLocalization?: string;
  articleSource?: string;
  avatar: string; // url
  thumbnail?: string; // url
  primaryColumn: ArticleColumn;
  primaryColumnColorCombo?: PrimaryColumnColorCombo;
  secondaryColumns?: ArticleColumn[];
  tags: Tag[];
  games?: Game[];
  body: ArticleBody[];
  minuteToMinute: MinuteToMinuteState;
  minuteToMinuteBlocks?: MinuteToMinuteBlock[];
  isOpinion: boolean;
  withoutAds: boolean;
  isExceptionAdvertEnabled: boolean;
  sponsorship?: Sponsorship;
  avCodes?: string;
  recommendedArticlesAvCodes?: string;
  ctLinkCodes?: string;
  isNotebook: boolean;
  isEditorMessageable: boolean;
  isAdultsOnly: boolean;
  isPodcastType?: boolean;
  isVideoType?: boolean;
  videoType?: boolean;
  isInterviewType?: boolean;
  preTitle?: string;
  year: number;
  month: number;
  dontMissThisArticle?: EmbeddedArticleRecommendation;
  tag?: Tag;
  excerpt?: string;
  columnSlug?: string;
  columnTitle?: string;
  isAlternativeView?: boolean;
  thumbnailInfo?: ThumbnailInfo;
  metaThumbnail?: string;
  secondaryThumbnail?: string;
  slug?: string; // Missing from the Article -> temporarily added for SSR build
  dossier?: BasicDossier;
  alternativeTitle?: string;
  technicalMetaTag?: string;
  hideThumbnailFromBody?: boolean;
  aniCode?: string;
  canonicalUrl?: string;
  regionTitle?: string;
  regionSlug?: string;
  regions?: Region[];
  seo?: ArticleSeoFields;
  robotsTag?: string;
  likeCount?: number;
  dislikeCount?: number;
  commentCount?: number;
  readingTime?: number;
  publicAuthorIsInner?: boolean;
  articleMedium?: string;
  isPaywalled?: boolean;
  isCommentsDisabled?: boolean;
  isProtectedContent?: boolean;
  topicLevel1?: string;
  topicLevel2?: string;
  topicLevel3?: string;
  embedPrAdvert?: string;
  videoLead?: {
    position?: 'below_title' | 'above_title';
    video?: VideoComponentObject;
  };
  materialType?: 'own_material' | 'news_service_material' | 'other_material';
  isShortNewsType?: boolean;
  printTitle?: string | null;
  subTitle?: string | null;
  languages?: ArticleLanguage[];
  publicAuthorM2M?: PublicMultiAuthor[];
  foundationTagSlug?: string;
  foundationTagTitle?: string;
  minuteToMinuteTimeHidden?: boolean;
  isGuaranteeType?: boolean;
  firstPublishDate?: Date;
  thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
  secondaryThumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
}>;

export type ArticleSeoFields = Readonly<{
  seoTitle?: string;
  seoDescription?: string;
  seoMainKeyword?: string;
  seoOtherKeywords?: string;
  seoLongTailKeywords?: string;
  seoCanonicalUrl?: string;
  seoRobotsMeta?: string;
  facebookTitle?: string;
  seoImage?: string;
  seoCategorySelector?: string;
  seoResearchSource?: string;
}>;

export type PrimaryColumnColorCombo = Readonly<{
  color: string;
  background: string;
}>;

export type Sponsorship = Readonly<{
  title?: string;
  thumbnailUrl?: string;
  url?: string;
  highlightedColor?: string;
  fontColor?: string;
}>;

export type RatingElementData = Readonly<{
  type: ArticleBodyType.Rating;
  id: string;
  details: RatingDetail[];
  subComponents: ComponentData[];
}>;

export type RatingDetail = Readonly<{
  key: 'rating';
  type: 'Rating.RatingDetail';
  value: Rating;
}>;

export type Rating = Readonly<{
  title?: string;
  value?: number;
}>;

export type TenArticleRecommenderData = Readonly<{
  type: ArticleBodyType.TenArticleRecommender;
  id: string;
  details:
    | (
        | TenArticleRecommenderDetailTitleDetail
        | TenArticleRecommenderDetailLeadDetail
        | TenArticleRecommenderDetailWithImageDetail
        | DetailReferenceArticle
        | DetailReferenceArticleOptional
      )[]
    | []
    | null;
  subcomponents: ComponentData[];
}>;

export type TenOpinionRecommenderData = Readonly<{
  type: ArticleBodyType.TenOpinionRecommender;
  id: string;
  details:
    | (
        | TenOpinionRecommenderDetailTitleDetail
        | TenOpinionRecommenderDetailLeadDetail
        | TenOpinionRecommenderDetailWithImageDetail
        | DetailReferenceOpinionArticle
        | DetailReferenceOpinionArticleOptional
      )[]
    | []
    | null;
  subcomponents: ComponentData[];
}>;

export type TenArticleRecommenderDetailTitleDetail = Readonly<{
  key: string;
  type: 'TenArticleRcm.TenArticleTitleDetail';
  value: string | null;
}>;

export type TenOpinionRecommenderDetailTitleDetail = Readonly<{
  key: string;
  type: 'TenOpinionArticleRcm.TenOpinionArticleTitleDetail';
  value: string | null;
}>;

export type TenArticleRecommenderDetailLeadDetail = Readonly<{
  key: string;
  type: 'TenArticleRcm.TenArticleLeadDetail';
  value: string | null;
}>;

export type TenOpinionRecommenderDetailLeadDetail = Readonly<{
  key: string;
  type: 'TenOpinionArticleRcm.TenOpinionArticleLeadDetail';
  value: string | null;
}>;

export type TenArticleRecommenderDetailWithImageDetail = Readonly<{
  key: string;
  type: 'TenArticleRcm.TenArticleWithImageDetail';
  value: boolean;
}>;
export type TenOpinionRecommenderDetailWithImageDetail = Readonly<{
  key: string;
  type: 'TenOpinionArticleRcm.TenOpinionArticleWithImageDetail';
  value: boolean;
}>;

export type DetailReferenceArticle = Readonly<{
  key: string;
  type: 'Detail.Reference.Article';
  value: PreviewBackendArticle;
}>;

export type DetailReferenceOpinionArticle = Readonly<{
  key: string;
  type: 'Detail.Reference.OpinionArticle';
  value: PreviewBackendArticle;
}>;

export type DetailReferenceArticleOptional = Readonly<{
  key: string;
  type: 'Detail.Reference.ArticleOptional';
  value: PreviewBackendArticle;
}>;

export type DetailReferenceOpinionArticleOptional = Readonly<{
  key: string;
  type: 'Detail.Reference.OpinionArticleOptional';
  value: PreviewBackendArticle;
}>;

export type ArticleStatus = Readonly<{
  opinionType?: boolean;
  articleMedium?: string;
  isAdultsOnly?: boolean;
}>;

export type ArticleDbCache = Readonly<{
  publicAuthor: string;
  publicAuthorAvatarThumbnailUrl: string;
  isPodcastType: boolean;
  isVideoType: boolean;
  hasGallery?: boolean;
}>;

export type PreviewBackendArticle = Readonly<{
  id: string;
  title: string;
  preTitle?: string;
  excerpt?: string;
  slug: string;
  publishDate: string | BackendDate;
  primaryColumn: ArticleColumn;
  column: ArticleColumn;
  thumbnailUrl: string;
  lead: string;
  status?: ArticleStatus;
  dbcache?: ArticleDbCache;
  publicAuthor?: string;
  foundationTagSlug?: string;
  foundationTagTitle?: string;
  thumbnailUrlFocusedImages?: FocusPointUrlWithAspectRatio;
}>;

export type PreviewBackendVoteArticle = Readonly<{
  column: { title: string; slug: string };
  preTitle: string;
  publishDate: string;
  primaryColumn?: ArticleColumn;
  slug: string;
  thumbnailUrl: string;
  title: string;
}>;

export type PublicMultiAuthor = Readonly<{
  avatar: string;
  email?: string;
  facebook?: string;
  firstName?: string;
  fullName: string;
  id: string;
  instagram?: string;
  isInner?: boolean;
  lastName?: string;
  slug: string;
  tiktok?: string;
  rank?: string;
  description?: string;
}>;

export type EmbeddedArticleRecommendation = Readonly<{
  columnSlug: string;
  publishDate: string;
  slug: string;
  title: string;
}>;

export type BackendArticle = PreviewBackendArticle &
  Readonly<{
    printTitle?: string | null;
    lead: string;
    publishDate: string | BackendDate;
    lastUpdated: string;
    publicAuthor?: string;
    publicAuthorEmail?: string;
    publicAuthorTitle?: string;
    publicAuthorLocalization?: string;
    publicAuthorDescription?: string;
    articleSource?: string;
    avatar: string; // url
    thumbnail?: string; // url
    primaryColumn: ArticleColumn;
    secondaryColumns: ArticleColumn[];
    tags: Tag[];
    body: ArticleBody[];
    minuteToMinute: MinuteToMinuteState;
    minuteToMinuteBlocks?: MinuteToMinuteBlock[];
    isOpinion: boolean;
    withoutAds: boolean;
    isExceptionAdvertEnabled: boolean;
    sponsorship?: Sponsorship;
    avCodes?: string;
    recommendedArticlesAvCodes?: string;
    ctLinkCodes?: string;
    isNotebook: boolean;
    isEditorMessageable: boolean;
    isAdultsOnly: boolean;
    isPodcastType?: boolean;
    isVideoType?: boolean;
    dossier?: BasicDossier[];
    dontMissThisArticle?: EmbeddedArticleRecommendation;
    region?: Region;
    firstTagId?: string;
    firstRegionId?: string;
    foundationTagSlug?: string;
    foundationTagTitle?: string;
    podcastType?: boolean;
    videoType?: boolean;
  }>;

export enum MinuteToMinuteState {
  NOT = 'not',
  RUNNING = 'running',
  CLOSED = 'closed',
}

export type MinuteToMinuteBlock = Readonly<{
  id: string;
  title: string;
  date: string | Date;
  body: ArticleBody[];
  buzzword?: string;
}>;

export type WysiwygMain = Readonly<{
  id: string;
  details: WysiwygText[];
  type: string;
  subComponents: ComponentData[];
}>;

export type WysiwygText = Readonly<{
  type: string;
  key: string;
  value: string;
}>;

export type ComponentData = Readonly<{
  type: string;
  uuid: string;
  key: string;
  subComponents: ComponentData[];
  details: ComponentFieldData[];
  autoplay?: boolean;
  adId?: number;
  cssClass?: string;
}>;

export type ComponentFieldData = Readonly<{
  type: string;
  inputType: string;
  key: string;
  uuid: string;
  value: any;
  multiple?: boolean;
  properties?: any;
  valueDetails?: any;
}>;

export type RecommendedArticle = Readonly<{
  id: string;
  publishDate: Date;
  title: string;
  preTitle?: string;
  excerpt: string;
  lead?: string;
  slug: string;
  thumbnailUrl: string;
  readingLength: number;
  columnId: string;
  columnTitle: string;
  columnSlug: string;
  preTitleColor?: string;
  columnTitleColor?: string;
  titleColor?: string;
  regions?: Region[];
  tags?: Tag[];
  thumbnail?: {
    url: string;
  };
  foundationTagSlug?: string;
  foundationTagTitle?: string;
}>;

export type BackendRecommendedArticle = RecommendedArticle &
  Readonly<{
    publishDate?: string;
    thumbnail?: string;
    thumbnailUrlFocusedImages?: FocusPointUrlWithAspectRatio;
  }>;

export type ExternalRecommendation = Readonly<{
  spr: string;
  imagePath: string;
  siteName: string;
  title: string;
  url: string;
}>;

export type BackendRecommendationsData = Readonly<{
  highPriorityArticles: BackendRecommendedArticle[];
  lowPriorityArticles: BackendRecommendedArticle[];
  lastThreeDaysMostReadArticles: BackendRecommendedArticle[];
  videos: ArticleCard[];
  externalRecommendation: ExternalRecommendation[];
  categoryArticles?: ArticleCard[];
  articlesByTags?: BackendRecommendedArticle[];
}>;

export type RecommendationsData = Readonly<{
  highPriorityArticles: ArticleCard[];
  lowPriorityArticles: ArticleCard[];
  lastThreeDaysMostReadArticles: ArticleCard[];
  videos: ArticleCard[];
  externalRecommendation: ArticleCard[];
  categoryArticles?: ArticleCard[];
  articlesByTags?: ArticleCard[];
}>;

export type ArticleResolverData = Readonly<{
  article: ApiResult<Article> & { articles?: ApiResult<ArticleCard[]> };
  recommendations: ApiResult<RecommendationsData>;
  relatedArticles?: ApiResult<ArticleSearchResult[]>;
  year?: string;
  month?: string;
  articleSlug: string;
  categorySlug: string;
  url?: string;
  newestArticles?: ArticleCard[];
}>;

export type AllColumnsResponse = Readonly<{
  data: AllColumns[];
  meta: ApiResponseMetaList;
}>;

export type AllColumns = Readonly<{
  id: string;
  title: string;
  titleColor?: string;
  slug: string;
  description: string;
  showInRss: string;
  parentId: string;
  parentTitle: string;
  parentDescription: string;
  parentShowInRss: any;
  data: any;
}>;

export type ArticleCard = ArticleSocial &
  PriorityContentFlags &
  Readonly<{
    id?: string;
    title: string;
    preTitle?: string;
    subTitle?: string;
    excerpt?: string;
    slug?: string;
    category?: ArticleCategory;
    publishDate?: Date | string;
    label?: Label;
    labelText?: string;
    publishYear?: string | number;
    publishMonth?: string | number;
    preTitleColor?: string;
    readingTime?: string;
    length?: number;
    lead?: string;
    thumbnailUrlHuge?: string;
    thumbnail?: ThumbnailImage;
    secondaryThumbnail?: ThumbnailImage;
    thumbnailUrl?: string;
    thumbnailUrl43?: string;
    author?: Author;
    sponsorId?: string;
    sponsorTitle?: string;
    sponsorUrl?: string;
    contentType?: string;
    primaryColumnColorCombo?: PrimaryColumnColorCombo;
    columnTitle?: string;
    columnSlug?: string;
    regionTitle?: string;
    regionSlug?: string;
    leadingArticle?: boolean;
    url?: string;
    tag?: Tag;
    tags?: Tag[];
    isVideoType?: boolean;
    isAdultsOnly?: boolean | FakeBool;
    isPodcastType?: boolean | FakeBool;
    backgroundColor?: string;
    foregroundColor?: string;
    titleBackgroundColor?: string;
    color?: string;
    hasLenia?: boolean;
    brandingType?: string;
    customTag?: string;
    regions?: Region[];
    firstRegionId?: string;
    firstTagId?: string;
    firstTagTitle?: string;
    hasGallery?: boolean | FakeBool;
    isOpinion?: boolean;
    publicAuthorM2M?: PublicMultiAuthor[];
    minuteToMinute?: MinuteToMinuteState;
    articleSource?: string;
    articleMedium?: string;
    hasDossiers?: boolean;
    isPaywalled?: boolean;
    fontSize?: number;
    isProtectedContent?: boolean;
    isVideo?: FakeBool;
    brand?: string;
    recommendedTitle?: string;
    publicAuthor?: string;
    publicAuthorSlug?: string;
    publicAuthorLocalization?: string;
    customLink?: {
      url?: string;
      openInNewTab?: boolean;
    };
    podcastType?: PodcastType;
    foundationTagSlug?: string;
    foundationTagTitle?: string;
    podcastGuestName?: string;
    podcastGuestNameTitle?: string;
    podcastGuestAvatarThumbnailUrl?: string;
    columnEmphasizeOnArticleCard?: boolean;
    highlightedImageUrl?: string;
    highlightedImage?: string | { url?: string; alt?: string };
    dbcache?: ArticleDbCache;
    status?: ArticleStatus;
    isGuaranteeType?: boolean;
    blockTitle?: BlockTitle;
    thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
    secondaryThumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
    avatarImageFocusedImages?: FocusPointUrlWithAspectRatio;
    thumbnailUrlFocusedImages?: FocusPointUrlWithAspectRatio;
    isVidcast?: boolean;
    gameLogoUrl?: string;
    gameBackgroundColor?: string;
    gameTitle?: string;
    isExclusive?: boolean;
    isBreaking?: boolean;
    columnMainColor?: string;
  }>;

export type ArticleSocial = Readonly<{
  likeCount?: number;
  dislikeCount?: number;
  commentCount?: number;
  isCommentsDisabled?: boolean;
  isLikesAndDislikesDisabled?: boolean;
}>;

export type ArticleCardWithSocial = ArticleCard & ArticleSocial;

export type FreshBlockCard = Readonly<{
  styleId?: number;
  preTitle?: string;
  title: string;
  lead?: string;
  hasVideo: boolean;
  columnSlug?: string;
  slug: string;
  publishDate?: string;
}>;

export type BackendDossierArticle = Readonly<{
  id: string;
  contentType: ContentType;
  title: string;
  publishDate: string;
  slug: string;
  lead: string;
  length: string;
  thumbnail?: string;
  thumbnailCreatedAt?: string;
  columnId: string;
  columnTitle: string;
  columnSlug: string;
}>;

export type DossierArticle = Readonly<{
  id: string;
  contentType: ContentType;
  title: string;
  preTitle?: string;
  publishDate?: Date;
  slug: string;
  lead: string;
  length?: number;
  thumbnail?: string;
  thumbnailCreatedAt?: Date;
  columnId: string;
  columnTitle: string;
  columnSlug: string;
  regionTitle?: string;
  regionSlug?: string;
  regions: Region[];
  tags: Tag[];
  isAdultsOnly?: FakeBool;
  isPodcastType?: FakeBool;
  hasGallery?: FakeBool;
  thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
}>;

export interface SubsequentDossier<D extends string | Date = string> {
  coverImage: string;
  headerColor: string;
  id: string;
  isActive: boolean;
  isDeleted: string;
  slug: string;
  relatedArticles: DossierRelatedArticle<D>[];
  title: string;
}

export interface DossierRelatedArticle<D extends string | Date = string> {
  columnId: string;
  columnParentId?: string;
  columnParentSlug?: string;
  columnParentTitle?: string;
  columnSlug: string;
  columnTitle: string;
  publishDate: D;
  slug: string;
  id: string;
  title: string;
}

export interface BasicDossierArticle<D extends string | Date = string> {
  title: string;
  slug: string;
  columnSlug: string;
  publishDate: D;
  name?: string;
  color?: string;
}

export interface BasicDossier<D extends string | Date = string> {
  articles?: BasicDossierArticle<D>[];
  title: string;
  slug: string;
  thumbnailUrl?: string;
  thumbnail?: string;
  headerImage?: string; // DEPRECATED - only used in CMS
  coverImage?: {
    // For ckeditor custom components
    id?: string;
    fullSizeUrl?: string;
    thumbnailUrl?: string;
  };
}

export type BreakingNews = Pick<
  InitResponseBreakingNews,
  | 'slug'
  | 'title'
  | 'excerpt'
  | 'columnSlug'
  | 'columnTitle'
  | 'thumbnailUrl'
  | 'lead'
  | 'publicAuthor'
  | 'publicAuthorEmail'
  | 'publicAuthorAvatarThumbnailUrl'
  | 'publicAuthorFullSizeUrl'
> &
  Readonly<{
    readingLength: number;
    publishDate: Date;
  }>;

export interface ColumnsQueryParams {
  rowCount_limit?: string;
  'ids[]'?: string[];
}

export type ArticleResponse = Readonly<{
  data: ArticleCard[];
  meta: Meta & {
    excludedIds?: string[];
  };
}>;

export interface RecentArticlesQueryParams {
  columnId?: string;
  columnSlug?: string;
  tagId?: string;
  tagSlug?: string;
  priority?: string;
  rowCount_limit?: string;
  page_limit?: string;
}

export interface ArticlesByDate {
  date: string;
  articles: ArticleCard[];
}

export type PromotionDetail = Readonly<{
  key: string;
  type: ArticleBodyDetailType.PromotionDetail;
  value: Promotion;
}>;

export type PromotionData = Readonly<{
  type: ArticleBodyType.Promotion;
  id: string;
  details: PromotionDetail[];
  subcomponents: ComponentData[];
}>;
