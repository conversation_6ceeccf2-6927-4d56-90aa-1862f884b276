import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, effect, inject, On<PERSON><PERSON>roy, OnInit, Signal } from '@angular/core';
import {
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  getNewestArticleThumbnail,
  PagerComponent,
  SearchFilterComponent,
} from '../../shared';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { SearchData } from './search.definitions';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta } from '@trendency/kesma-ui';
import { SlicePipe } from '@angular/common';
import { toNumber } from 'lodash-es';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { IMetaData, SeoService } from '@trendency/kesma-core';

const MAX_RESULTS_PER_PAGE = 10;

@Component({
  selector: 'app-search',
  imports: [PagerComponent, SearchFilterComponent, ArticleCardComponent, SlicePipe, BreadcrumbComponent, SidebarComponent, AdvertisementAdoceanComponent],
  templateUrl: './search.component.html',
  styleUrl: './search.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  readonly ArticleCardType = ArticleCardType;

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as SearchData)));

  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.articles);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.limitable);
  readonly globalFilter = computed(() => {
    const globalFilter = this.resolverData()?.globalFilter;
    this.setMetaData(globalFilter);
    return globalFilter;
  });
  readonly page = toSignal(this.route.queryParamMap.pipe(map((params) => toNumber(params.get('page')) ?? 0)));
  resultsCount: number = 0;

  adverts?: AdvertisementsByMedium;
  private readonly destroy$ = new Subject<void>();

  constructor() {
    effect(() => {
      const limitable = this.limitable();
      if (limitable) {
        this.calculateResultsCount();
      }
    });
  }

  ngOnInit(): void {
    this.initAds();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private calculateResultsCount(): void {
    const page = (this.page() ?? 0) + 1;
    const pageMaxResults = page * MAX_RESULTS_PER_PAGE;
    const rowAllCount = this.limitable()?.rowAllCount;
    const rowOnPageCount = this.limitable()?.rowOnPageCount ?? 0;

    if (rowAllCount && rowAllCount > 0) {
      this.resultsCount = pageMaxResults < rowAllCount ? rowOnPageCount * page : rowAllCount;
    } else {
      this.resultsCount = rowAllCount ?? 0;
    }
  }

  private setMetaData(globalFilter?: string): void {
    const plainTitle = globalFilter ? `Keresés: ${globalFilter}` : 'Keresés';
    const title = createBorsOnlineTitle(plainTitle);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      ogImage: getNewestArticleThumbnail(this.articles() ?? [], this.seo.hostUrl),
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('kereses');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  private initAds(): void {
    this.resetAds();
    this.adStoreAdo.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads)))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.cdr.detectChanges();
      });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }
}
