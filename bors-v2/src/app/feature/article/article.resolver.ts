import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { ApiResult, Article, ArticleRouteParams } from '@trendency/kesma-ui';
import { forkJoin, Observable, of, switchMap, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleService } from '../../shared';
import { environment } from '../../../environments/environment';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import type { Response } from 'express';

type ArticleResolverResponse = Readonly<{
  article: ApiResult<Article>;
  url?: string;
}>;

export const articleResolver: ResolveFn<ArticleResolverResponse> = (route: ActivatedRouteSnapshot) => {
  const router = inject(Router);
  const articleService = inject(ArticleService);
  const utilService = inject(UtilService);
  const seoService = inject(SeoService);
  const response = inject(RESPONSE, { optional: true });

  const params = route.params as ArticleRouteParams;
  const previewType = params?.previewType || 'accepted';
  const { previewHash, categorySlug, articleSlug } = params;
  const isYear = !isNaN(parseInt(params.year as string));
  const year = isYear && params.year ? params.year : undefined;
  const month = isYear && params.month ? params.month : undefined;
  const url = `${categorySlug}/${year}/${month}/${articleSlug}`;

  const request$: Observable<ArticleResolverResponse> = of({}).pipe(
    switchMap(() => {
      if (previewHash) {
        return forkJoin({
          article: articleService.getArticlePreview('cikk-elonezet', previewHash, previewType),
        });
      }
      if (!year || !month) {
        return forkJoin({
          article: redirectOldArticleUrls(articleService, utilService, seoService, response as Response, router),
        }) as Observable<ArticleResolverResponse>;
      }
      return forkJoin({
        article: articleService.getArticle(categorySlug, String(year), String(month), articleSlug),
        url: of(url),
      });
    })
  );

  return request$.pipe(
    catchError(() => {
      return redirectOldArticleUrls(articleService, utilService, seoService, response as Response, router);
    })
  ) as Observable<ArticleResolverResponse>;
};

const redirectOldArticleUrls = (
  articleService: ArticleService,
  utilService: UtilService,
  seoService: SeoService,
  response: Response,
  router: Router
): Observable<Object> => {
  const currentUrl = seoService.currentUrl;
  return articleService.getArticleRedirect(encodeURIComponent(currentUrl)).pipe(
    map(({ url }) => {
      if (url && utilService.isBrowser()) {
        window.location.href = url;
      } else if (url && response) {
        response.status(301);
        if (url.match(/^https?:\/\/localhost\//)) {
          url = url.replace(/^https?:\/\/localhost/, environment.siteUrl as string);
        }
        response.setHeader('location', url);
      } else {
        router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then();
        return throwError(() => null);
      }
      return of({});
    })
  );
};
