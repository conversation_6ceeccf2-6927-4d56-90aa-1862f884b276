import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, On<PERSON><PERSON>roy, OnInit, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ArticleCard, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import {
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  PagerComponent,
  PageTitleComponent,
} from '../../shared';
import { ApiResponseMetaList } from '@trendency/kesma-ui/lib/definitions/api-result';
import { SlicePipe, TitleCasePipe } from '@angular/common';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

type DossierListMeta = ApiResponseMetaList & {
  title?: string;
  description?: string;
};

@Component({
  selector: 'app-dossier-list',
  templateUrl: './dossier-list.component.html',
  styleUrl: './dossier-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, BreadcrumbComponent, TitleCasePipe, PageTitleComponent, SlicePipe, PagerComponent, SidebarComponent, AdvertisementAdoceanComponent],
})
export class DossierListComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly resolverData = toSignal(
    this.route.data.pipe(
      map(({ data }) => data),
      tap(({ meta }) => this.setMetaData(meta.title))
    )
  );

  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData().data);
  readonly meta: Signal<DossierListMeta | undefined> = computed(() => this.resolverData().meta);

  readonly ArticleCardType = ArticleCardType;

  adverts?: AdvertisementsByMedium;
  private readonly destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.initAds();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setMetaData(pageTitle: string): void {
    const canonical = createCanonicalUrlForPageablePage('dosszie', this.route.snapshot, {
      skipCategorySlug: true,
    });
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle(pageTitle);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
    };
    this.seoService.setMetaData(metaData);
  }

  private initAds(): void {
    this.adStoreAdo.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads)))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.cdr.detectChanges();
      });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }
}
