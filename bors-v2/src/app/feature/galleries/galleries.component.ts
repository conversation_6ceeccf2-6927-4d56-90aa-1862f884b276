import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, OnD<PERSON>roy, OnInit, signal, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ApiResponseMetaList, ApiResult, createCanonicalUrlForPageablePage, GalleryData, LimitableMeta } from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import { SeoService, StorageService } from '@trendency/kesma-core';
import {
  ADULT_CHOICE_STORAGE_KEY,
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  PagerComponent,
  PageTitleComponent,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { SlicePipe } from '@angular/common';

@Component({
  selector: 'app-galleries',
  templateUrl: './galleries.component.html',
  styleUrl: './galleries.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PagerComponent, BreadcrumbComponent, PageTitleComponent, SidebarComponent, SlicePipe, ArticleCardComponent, AdvertisementAdoceanComponent],
})
export class GalleriesComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly storageService = inject(StorageService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly ArticleCardType = ArticleCardType;

  readonly isUserAdultChoice = signal<boolean>(false);

  readonly resolverData: Signal<ApiResult<GalleryData[], ApiResponseMetaList>> = toSignal(
    this.route.data.pipe(
      tap(() => {
        this.setMetaData();
        this.isUserAdultChoice.set(this.storageService.getSessionStorageData(ADULT_CHOICE_STORAGE_KEY, false) ?? false);
      }),
      map(({ data }) => data)
    )
  );

  readonly galleries: Signal<GalleryData[] | undefined> = computed(() => this.resolverData()?.data);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.meta?.limitable);

  adverts?: AdvertisementsByMedium;
  private readonly destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.initAds();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setMetaData(): void {
    const title = createBorsOnlineTitle('Galériák');
    const canonical = createCanonicalUrlForPageablePage('galeriak', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
  }

  private initAds(): void {
    this.resetAds();
    this.adStoreAdo.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads)))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.cdr.detectChanges();
      });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }
}
