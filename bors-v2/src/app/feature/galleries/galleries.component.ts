import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnDestroy, OnInit, signal, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ApiResponseMetaList, ApiResult, createCanonicalUrlForPageablePage, GalleryData, LimitableMeta } from '@trendency/kesma-ui';
import { map, tap } from 'rxjs/operators';
import { SeoService, StorageService } from '@trendency/kesma-core';
import {
  ADULT_CHOICE_STORAGE_KEY,
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  PagerComponent,
  PageTitleComponent,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { SlicePipe } from '@angular/common';

@Component({
  selector: 'app-galleries',
  templateUrl: './galleries.component.html',
  styleUrl: './galleries.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PagerComponent, BreadcrumbComponent, PageTitleComponent, SidebarComponent, SlicePipe, ArticleCardComponent, AdvertisementAdoceanComponent],
})
export class GalleriesComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly storageService = inject(StorageService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly destroyRef = inject(DestroyRef);

  readonly ArticleCardType = ArticleCardType;

  readonly isUserAdultChoice = signal<boolean>(false);

  readonly resolverData: Signal<ApiResult<GalleryData[], ApiResponseMetaList>> = toSignal(
    this.route.data.pipe(
      tap(() => {
        this.setMetaData();
        this.isUserAdultChoice.set(this.storageService.getSessionStorageData(ADULT_CHOICE_STORAGE_KEY, false) ?? false);
      }),
      map(({ data }) => data)
    )
  );

  readonly galleries: Signal<GalleryData[] | undefined> = computed(() => this.resolverData()?.data);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.meta?.limitable);

  readonly adverts = toSignal(
    this.adStoreAdo.advertisemenets$.pipe(
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads)),
      takeUntilDestroyed(this.destroyRef)
    ),
    { initialValue: undefined }
  );

  ngOnInit(): void {
    // No need for initAds() anymore - using toSignal
  }

  ngOnDestroy(): void {
    // No cleanup needed - using takeUntilDestroyed
  }

  private setMetaData(): void {
    const title = createBorsOnlineTitle('Galériák');
    const canonical = createCanonicalUrlForPageablePage('galeriak', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
  }
}
