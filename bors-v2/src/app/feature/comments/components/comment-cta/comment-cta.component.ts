import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { BorsSimpleButtonComponent } from '../../../../shared';
import { CommentService } from '../../api/comment.service';

@Component({
  selector: 'app-comment-cta',
  imports: [BorsSimpleButtonComponent],
  templateUrl: './comment-cta.component.html',
  styleUrl: './comment-cta.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommentCtaComponent {
  protected readonly commentService = inject(CommentService);
}
