<section class="login-form">
  <div class="wrapper login-form-wrapper">
    <div class="login-form-header">
      <h2 class="login-form-header-title">Bejelentkezés</h2>
      <p class="login-form-header-text">
        <PERSON>é<PERSON><PERSON>n be <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, és has<PERSON> ki a regisztrált olvasóknak járó előnyöket! Ha még ninc<PERSON>,
        <a class="login-form-header-text-link" routerLink="/regisztracio"> itt regisztrálhat!</a>
      </p>
    </div>
    @if (formGroup() && allowedLoginMethods()) {
      <form (ngSubmit)="login()" [formGroup]="formGroup()">
        @if (allowedLoginMethods()?.email) {
          <app-input-control controlName="emailOrUsername" labelText="E-mail cím" placeholder="Adja meg az e-mail címét"></app-input-control>
          <app-password-control class="password-control" [hideErrors]="true" [showRecoveryLink]="true"></app-password-control>
          @if (error()) {
            <div class="general-form-error">{{ error() }}</div>
          }
          <div class="login-form-actions">
            <app-simple-button class="login-form-actions-button" [disabled]="isLoading()" [isSubmit]="true">
              {{ isLoading() ? 'Kérem, várjon...' : 'Bejelentkezem' }}
            </app-simple-button>
            @if (this.enableRememberMe()) {
              <kesma-form-control class="checkbox">
                <label class="bors-form-checkbox centered" for="rememberMe">
                  <input type="checkbox" id="rememberMe" formControlName="rememberMe" />
                  <span>Maradjon belépve</span>
                </label>
              </kesma-form-control>
            }
          </div>
        }
      </form>
    }
  </div>
</section>
