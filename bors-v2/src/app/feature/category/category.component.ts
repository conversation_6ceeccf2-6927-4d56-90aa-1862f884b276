import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, On<PERSON><PERSON>roy, OnInit, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ArticleCard, BreadcrumbItem, createCanonicalUrlForPageablePage, LayoutPageType } from '@trendency/kesma-ui';
import {
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  CategoryResolverResponse,
  createBorsOnlineTitle,
  defaultMetaInfo,
  HeaderService,
  PagerComponent,
} from '../../shared';
import { map, switchMap, tap } from 'rxjs/operators';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { NgClass, SlicePipe } from '@angular/common';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-category',
  imports: [ArticleCardComponent, BreadcrumbComponent, PagerComponent, SidebarComponent, SlicePipe, LayoutComponent, NgClass, AdvertisementAdoceanComponent],
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CategoryComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly headerService = inject(HeaderService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly destroyRef = inject(DestroyRef);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as CategoryResolverResponse)));

  readonly breadcrumbItems = computed(() => {
    const items: BreadcrumbItem[] = [];
    const columnParentTitle = this.resolverData()?.columnParentTitle;
    const columnParentSlug = this.resolverData()?.columnParentSlug;
    const currentColumnTitle = this.resolverData()?.columnTitle;
    if (columnParentTitle && columnParentSlug) {
      items.push({
        label: columnParentTitle,
        url: `/rovat/${columnParentSlug}`,
      });
    }
    if (currentColumnTitle) {
      items.push({
        label: currentColumnTitle,
      });
    }
    return items;
  });

  readonly currentPage = computed(() =>
    this.resolverData()?.category?.meta?.limitable?.pageCurrent ? this.resolverData()!.category.meta.limitable.pageCurrent + 1 : 1
  );

  readonly columnTitle: Signal<string | undefined> = computed(() => {
    const title = this.currentPage() > 1 ? `${this.currentPage()}. oldal - ${this.resolverData()?.columnTitle}` : this.resolverData()?.columnTitle;
    this.setMetaData(title);
    return title;
  });
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.category?.data);
  readonly layoutData = computed(() => this.resolverData()?.layoutApiResponse);
  readonly limitable = computed(() => this.resolverData()?.category?.meta?.limitable);
  readonly columnColor = computed(() => this.resolverData()?.columnColor);

  readonly ArticleCardType = ArticleCardType;
  readonly LayoutPageType = LayoutPageType;

  categoryTitle = '';
  adPageType = '';

  readonly adverts = toSignal(
    this.route.data.pipe(
      tap(({ data }) => {
        this.categoryTitle = data.columnTitle;
        this.adPageType = `column_${data.slug}`;
        this.adStoreAdo.setArticleParentCategory(this.adPageType);
      }),
      switchMap(() => this.adStoreAdo.advertisemenets$),
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads, this.adPageType)),
      takeUntilDestroyed(this.destroyRef)
    ),
    { initialValue: undefined }
  );

  ngOnInit(): void {
    // No need for initAds() anymore - using toSignal
  }

  ngOnDestroy(): void {
    this.headerService.setColor();
    this.adStoreAdo.setArticleParentCategory('');
  }

  private setMetaData(columnTitle?: string): void {
    if (!columnTitle) {
      return;
    }
    const title = createBorsOnlineTitle(columnTitle);

    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });

    const canonical = createCanonicalUrlForPageablePage('rovat', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
  }
}
