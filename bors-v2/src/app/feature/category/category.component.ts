import { ChangeDetectionStrategy, Component, computed, inject, On<PERSON><PERSON>roy, OnInit, Signal } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { DOCUMENT, NgIf } from '@angular/common';
import { combineLatest, interval, Observable, of } from 'rxjs';
import { delayWhen, filter, map, mergeMap, startWith, takeUntil } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementBannerName,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  ArticleCard,
  BreadcrumbItem,
  createCanonicalUrlForPageablePage,
  LayoutPageType,
  PAGE_TYPES,
  SecondaryFilterAdvertType,
  WINDOW,
} from '@trendency/kesma-ui';
import {
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  CategoryResolverResponse,
  createBorsOnlineTitle,
  defaultMetaInfo,
  HeaderService,
  PagerComponent,
} from '../../shared';
import { map } from 'rxjs/operators';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { NgClass, SlicePipe } from '@angular/common';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-category',
  imports: [ArticleCardComponent, BreadcrumbComponent, PagerComponent, SidebarComponent, SlicePipe, LayoutComponent, NgClass, AdvertisementAdoceanComponent, NgIf],
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CategoryComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly seo = inject(SeoService);
  private readonly headerService = inject(HeaderService);
  private readonly utils = inject(UtilService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly documentElement = inject(DOCUMENT);
  private readonly window = inject(WINDOW);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as CategoryResolverResponse)));

  readonly breadcrumbItems = computed(() => {
    const items: BreadcrumbItem[] = [];
    const columnParentTitle = this.resolverData()?.columnParentTitle;
    const columnParentSlug = this.resolverData()?.columnParentSlug;
    const currentColumnTitle = this.resolverData()?.columnTitle;
    if (columnParentTitle && columnParentSlug) {
      items.push({
        label: columnParentTitle,
        url: `/rovat/${columnParentSlug}`,
      });
    }
    if (currentColumnTitle) {
      items.push({
        label: currentColumnTitle,
      });
    }
    return items;
  });

  readonly currentPage = computed(() =>
    this.resolverData()?.category?.meta?.limitable?.pageCurrent ? this.resolverData()!.category.meta.limitable.pageCurrent + 1 : 1
  );

  readonly columnTitle: Signal<string | undefined> = computed(() => {
    const title = this.currentPage() > 1 ? `${this.currentPage()}. oldal - ${this.resolverData()?.columnTitle}` : this.resolverData()?.columnTitle;
    this.setMetaData(title);
    return title;
  });
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.category?.data);
  readonly layoutData = computed(() => this.resolverData()?.layoutApiResponse);
  readonly limitable = computed(() => this.resolverData()?.category?.meta?.limitable);
  readonly columnColor = computed(() => this.resolverData()?.columnColor);

  readonly ArticleCardType = ArticleCardType;
  readonly LayoutPageType = LayoutPageType;

  ngOnDestroy(): void {
    this.headerService.setColor();
  }

  private setMetaData(columnTitle?: string): void {
    if (!columnTitle) {
      return;
    }
    const title = createBorsOnlineTitle(columnTitle);

    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });

    const canonical = createCanonicalUrlForPageablePage('rovat', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
  }
}
