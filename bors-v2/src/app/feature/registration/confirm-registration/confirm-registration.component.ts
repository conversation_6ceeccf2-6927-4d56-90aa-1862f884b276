import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnDestroy, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Params, RouterLink } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, of, switchMap } from 'rxjs';
import { ApiService, BorsSimpleButtonComponent, RegRedirectToken } from '../../../shared';
import { StorageService } from '@trendency/kesma-core';

@Component({
  selector: 'app-confirm-registration',
  templateUrl: './confirm-registration.component.html',
  styleUrl: './confirm-registration.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, BorsSimpleButtonComponent],
})
export class ConfirmRegistrationComponent implements OnInit, OnDestroy {
  private readonly apiService = inject(ApiService);
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly storageService = inject(StorageService);

  readonly isLoading = signal<boolean>(true);
  readonly error = signal<string | null>(null);
  readonly redirectUrl = signal(this.storageService.getLocalStorageData(RegRedirectToken, null) ?? null);

  ngOnInit(): void {
    this.route.queryParams
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        switchMap((params: Params): Observable<void> => {
          const { id, hashedEmail, expiration, signature } = params;
          const hasError = !(id && hashedEmail && expiration && signature);

          if (hasError) {
            this.error.set('Hiba, érvénytelen link, kérjük ellenőrizze a böngészőben megadott hivatkozást!');
            this.isLoading.set(false);
            return of();
          }
          return this.apiService.verifyRegister(id, hashedEmail, expiration, signature);
        })
      )
      .subscribe({
        next: () => {
          this.error.set(null);
          this.isLoading.set(false);
        },
        error: () => {
          this.error.set('Hiba, a regisztráció megerősítésére kiküldött link érvénytelen vagy lejárt, kérem próbálja újra!');
          this.isLoading.set(false);
        },
      });
  }

  ngOnDestroy(): void {
    this.storageService.removeLocalStorageData(RegRedirectToken);
  }
}
