@use 'shared' as *;

:host {
  @include media-breakpoint-down(md) {
    display: flex;
    width: 100%;
    justify-content: center;
  }
}

.register-form {
  color: var(--kui-black-950);
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 640px;
  background-color: var(--kui-gray-100);
  margin: 60px auto;
  padding: 60px 0;

  @include media-breakpoint-down(md) {
    padding: 32px 0px;
    margin: 32px 16px 16px 16px;
  }

  &-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 32px;
    width: 100%;
    max-width: 416px;

    @include media-breakpoint-down(md) {
      padding: 0 16px;
    }
  }

  &-header {
    display: flex;
    flex-direction: column;
    gap: 60px;

    @include media-breakpoint-down(md) {
      gap: 32px;
    }

    &-title {
      font-size: 36px;
      font-weight: 800;
      text-align: center;
      line-height: 42px;

      @include media-breakpoint-down(md) {
        font-size: 26px;
        line-height: 28px;
      }
    }

    &-text {
      text-align: center;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
    }

    &-text-link {
      font-weight: 700;
      color: var(--kui-red-500);
      text-decoration: underline;
      line-height: 24px;
      &:hover {
        text-decoration: none;
        color: var(--kui-red-550);
      }
    }
  }

  &-privacy {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  form {
    display: flex;
    flex-direction: column;
    gap: 32px;
  }

  .bors-form-checkbox {
    span {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }

    &-label-link {
      color: var(--kui-red-500);
      text-decoration: underline;
      font-weight: 700;
      &:hover {
        text-decoration: none;
        color: var(--kui-red-550);
      }
    }
  }

  .login-prompt {
    text-align: center;
    margin-top: 12px;
    font-weight: bold;
  }
  .general-form-error {
    color: var(--kui-red-500);
    text-align: center;
  }
}
