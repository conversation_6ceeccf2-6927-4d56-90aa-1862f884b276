@use 'shared' as *;

:host {
  display: block;
  margin: auto;

  kesma-layout ::ng-deep {
    @include media-breakpoint-down(md) {
      margin-top: 15px;
    }

    .has-custom-background:nth-of-type(1) {
      @include media-breakpoint-down(md) {
        padding-top: 85px;
      }
    }
  }

  app-astronet-fortune {
    @include media-breakpoint-down(sm) {
      margin-inline: -16px;
      width: calc(100% + 32px);
    }
  }
}
