<div class="wrapper">
  <div class="meta">
    <a [routerLink]="['/rovat', mappedArticles[0]?.columnSlug]" class="title">
      {{ columnTitle() || mappedArticles[0]?.columnTitle }}
    </a>
    <a [routerLink]="['/rovat', mappedArticles[0]?.columnSlug]" class="more-button">
      Még {{ columnTitle() || mappedArticles[0]?.columnTitle }} <kesma-icon name="arrow-right-long" size="14px"></kesma-icon
    ></a>
  </div>
  <div class="articles">
    @for (article of mappedArticles; track article.id; let i = $index) {
      <app-article-card
        [data]="article"
        [styleId]="
          i < 2 || (i < 3 && desktopWidth() === 12)
            ? isBig
              ? ArticleCardType.TopSlantImgTagTitle
              : ArticleCardType.TopImgTagTitle
            : ArticleCardType.NoImgTagTitle
        "
        [hasLeftBorder]="i > 2 || (i >= 2 && desktopWidth() !== 12)"
        [forceThemeColorOnTag]="true"
        [hasBlockBackground]="true"
      ></app-article-card>
    }
  </div>
</div>
