import { ChangeDetectionStrategy, Component, HostBinding, input, OnChanges, signal } from '@angular/core';
import {
  AdultOverlayComponent,
  ArticleCard,
  backendDateToDate,
  BaseComponent,
  buildArticleUrl,
  buildTagUrl,
  FocusPointDirective,
  IconComponent,
  Tag,
  toBool,
} from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { BorsBlack, BorsWhite, PlaceholderImg } from '../../constants';
import { RouterLink } from '@angular/router';
import { NgTemplateOutlet } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';

@Component({
  selector: 'app-article-card',
  templateUrl: './article-card.component.html',
  styleUrl: './article-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, FocusPointDirective, NgTemplateOutlet, IconComponent, FormatPipeModule, AdultOverlayComponent],
})
export class ArticleCardComponent extends BaseComponent<ArticleCard> implements OnChanges {
  @HostBinding('style') get hostStyle(): string {
    const { foregroundColor, backgroundColor } = this.data as ArticleCard;
    return `
      ${this.isOpinion() ? '--theme-background: var(--kui-orange-100);' : backgroundColor ? `--theme-background: ${backgroundColor};` : ''}
      ${this.isOpinion() ? '--theme-color: var(--kui-black-950)' : foregroundColor ? `--theme-color: ${foregroundColor};` : ''}
      ${
        foregroundColor && this.isBreaking()
          ? foregroundColor === BorsWhite
            ? '--breaking-color: var(--kui-yellow-200);'
            : foregroundColor === BorsBlack
              ? '--breaking-color: var(--kui-red-500);'
              : ''
          : ''
      }
      ${this.isExclusive() ? '--exclusive-color: var(--kui-purple-exclusive-500);' : ''}
      ${
        this.data?.backgroundColor && !this.data?.columnMainColor
          ? `--tag-color: ${foregroundColor};`
          : this.data?.columnMainColor &&
              this.data?.columnMainColor?.toLowerCase() !== this.data?.backgroundColor?.toLowerCase() &&
              !this.forceThemeColorOnTag()
            ? `--tag-color: ${this.data.columnMainColor};`
            : '--tag-color: var(--theme-color);'
      }
      ${
        (this.data?.backgroundColor || this.hasBlockBackground()) &&
        (!foregroundColor || foregroundColor === BorsWhite || foregroundColor === 'var(--kui-white)')
          ? '--hover-color: var(--kui-white-o90)'
          : '--hover-color: var(--kui-black-1200)'
      }
    `;
  }

  @HostBinding('class') get hostClass(): string {
    return `
      style-${ArticleCardType[this.styleId()]}
      ${((this.data?.backgroundColor || this.isOpinion() || this.hasBlockBackground()) && 'has-background') || ''}
      ${(this.isSidebar() && 'is-sidebar') || ''}
    `;
  }

  styleId = input.required<ArticleCardType>();
  hasSponsorship = input<boolean>(false);
  isOpinion = input<boolean>(false);
  isExclusive = input<boolean>(false);
  isBreaking = input<boolean>(false);
  hasLeftBorder = input<boolean>(false);
  isSidebar = input<boolean>(false);
  desktopWidth = input<number>(12);
  showAdultLayer = input<boolean>(false);
  forceThemeColorOnTag = input<boolean>(false);
  useEagerLoad = input<boolean>(false);
  fetchpriority = input<string>('auto');
  hasBlockBackground = input<boolean>(false);

  displayedThumbnailUrl = signal<string>(PlaceholderImg);
  tagLink = signal<string[]>([]);
  articleLink = signal<string[]>([]);
  displayedTag = signal<Tag | undefined>(undefined);
  publishDate = signal<Date | null>(null);

  readonly PlaceholderImg = PlaceholderImg;
  readonly ArticleCardType = ArticleCardType;
  readonly toBool = toBool;

  ngOnChanges(changes: { data?: ArticleCard[] }): void {
    if (!changes?.data) {
      return;
    }
    //12=1/1 row/column
    if (this.desktopWidth() === 12 && this.styleId() === ArticleCardType.MainArticle) {
      this.displayedThumbnailUrl.set(
        this.data?.secondaryThumbnail?.fullSizeUrlHugeNative ||
          this.data?.secondaryThumbnail?.urlHuge ||
          this.data?.secondaryThumbnail?.url ||
          this.data?.thumbnailUrlHuge ||
          this.data?.thumbnail?.url ||
          PlaceholderImg
      );
      return;
    }
    this.displayedThumbnailUrl.set(this.data?.thumbnail?.url || PlaceholderImg);
  }

  separateBoldTitle(boldWordLength: number, title: string): object {
    const words = title.split(/\s+/);
    const boldWords = words.slice(0, boldWordLength);
    const normalWords = words.slice(boldWordLength);
    return {
      bold: boldWords.join(' '),
      normal: normalWords.join(' '),
    };
  }

  override setProperties(): void {
    if (!this.data) return;

    const { tags, publishDate, firstTagId } = this.data;

    const finalDate = publishDate instanceof Date ? publishDate : backendDateToDate(publishDate as string);
    this.publishDate.set(finalDate);

    const firstTag = tags?.find(({ id }) => id === firstTagId);
    this.displayedTag.set(firstTag ?? tags?.[0]);

    this.articleLink.set(this.data ? buildArticleUrl(this.data) : []);
    this.tagLink.set(this.data ? buildTagUrl(this.data) : []);
  }

  get hasBadges(): boolean {
    return [this.data?.isVideoType, this.data?.hasGallery, this.data?.isAdultsOnly].map(toBool).includes(true);
  }
}
