@use 'shared' as *;

.content-wrap {
  position: relative;
  max-width: $global-wrapper-width-with-bg;
  margin: auto;
  width: 100%;

  &.content-wrap-full-width {
    width: 100%;
  }
}

.homepage {
  margin: -125px auto auto auto;

  @include media-breakpoint-down(md) {
    width: 100%;
    margin: -65px auto auto auto;
  }
}

kesma-mediaworks-footer-compact,
app-footer {
  @include media-breakpoint-up(lg) {
    margin: 0px -32px;
  }
}

kesma-mediaworks-footer-compact {
  background-color: var(--kui-gray-900);
  padding: 50px 32px 60px;

  @include media-breakpoint-down(md) {
    padding-inline: 16px;
  }
}
