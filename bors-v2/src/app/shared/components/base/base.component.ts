import { DOCUMENT, NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterModule } from '@angular/router';
import { SeoService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementBannerName,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  BreakingBlock,
  InitResolverData,
  MediaworksFooterCompactComponent,
  PAGE_TYPES,
  PortfolioItem,
  PortfolioResponse,
  SecondaryFilterAdvertType,
  SimplifiedMenuItem,
  WINDOW,
} from '@trendency/kesma-ui';
import { combineLatest, interval, Observable, of, take } from 'rxjs';
import { delayWhen, filter, map, mergeMap, startWith } from 'rxjs/operators';
import { ApiService, AuthService, HeaderService, UrlService } from '../../services';
import { BreakingNewsComponent } from '../breaking-news/breaking-news.component';
import { FooterComponent } from '../footer/footer.component';
import { HeaderComponent } from '../header/header.component';

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  imports: [RouterModule, MediaworksFooterCompactComponent, FooterComponent, BreakingNewsComponent, HeaderComponent, AdvertisementAdoceanComponent, NgIf],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseComponent implements OnInit, AfterViewInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly utils = inject(UtilService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly apiService = inject(ApiService);
  private readonly documentElement = inject(DOCUMENT);
  private readonly window = inject(WINDOW);
  private readonly authService = inject(AuthService);
  private readonly headerService = inject(HeaderService);
  private readonly urlService = inject(UrlService);
  private readonly seoService = inject(SeoService);

  readonly mainMenu = signal<SimplifiedMenuItem[]>([]);
  readonly topMenu = signal<SimplifiedMenuItem[]>([]);
  readonly footer = signal<SimplifiedMenuItem[]>([]);

  readonly ads = signal<Record<string, Advertisement> | undefined>(undefined);
  readonly breakingNews = signal<BreakingBlock | undefined>(undefined);
  readonly isArticleUrl = signal<boolean>(false);

  readonly isAdblockerActive = signal<boolean>(false);

  readonly mediaworksFooter = signal<PortfolioItem[]>([]);
  readonly isHomePage = computed(() => this.headerService.isHomePage());

  adverts?: AdvertisementsByMedium;
  backgroundAdvert?: Record<'desktop' | 'mobile', Advertisement>;
  areAdsInitiated = false;

  ngOnInit(): void {
    // Check user at first page load (async, not to block UI) to display header differently if user is logged in
    this.authService.isAuthenticated().subscribe();
    this.urlService.setPreviousUrl(this.seoService.getCurrentRelativeUrl());

    // any is necessary due to missing overlap of `NavigationEnd` and result of `router.events`
    combineLatest([this.router.events as Observable<any | NavigationEnd>, this.adStoreAdo.isAdult.asObservable()])
      .pipe(
        filter<[any | NavigationEnd, boolean]>(([event]) => event instanceof NavigationEnd),
        startWith([null, false]),
        mergeMap(([event]) => {
          if (!this.utils.isBrowser() || !this.documentElement?.location) {
            return of({} as AdvertisementsByMedium);
          }

          if (event?.url) {
            this.urlService.setPreviousUrl(event.url);
          }

          const [_, path1, path2] = this.documentElement?.location?.pathname.split('/') ?? ['', ''];

          this.isArticleUrl.set(!isNaN(parseInt(path2, 10)));
          this.resetAds();

          if (this.isArticleUrl()) {
            this.adStoreAdo.currentMasterIdSubject.next('');
          }

          return this.adStoreAdo.advertisemenets$.pipe(
            map<Advertisement[], AdvertisementsByMedium>((ads) => {
              const headerMediumSeparator = this.baseElementPageTypeSwitch(path1);

              return this.adStoreAdo.separateAdsByMedium(
                ads,
                headerMediumSeparator.page,
                headerMediumSeparator.ads,
                SecondaryFilterAdvertType.REPLACEABLE,
                PAGE_TYPES.other_pages
              );
            }),
            delayWhen((ads) =>
              this.isArticleUrl()
                ? this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.desktop?.layer?.masterId ||
                  this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.mobile?.layer?.masterId
                  ? interval(1000)
                  : interval(0)
                : interval(0)
            )
          );
        })
      )
      .subscribe((adverts) => {
        this.adverts = adverts;
        const backgroundAd = 'background' as AdvertisementBannerName;
        this.backgroundAdvert = {
          desktop: adverts?.desktop?.[backgroundAd],
          mobile: adverts?.mobile?.[backgroundAd],
        };

        // Keep the existing ads signal for backward compatibility
        this.ads.set({
          leaderboard_2_bottom: adverts?.desktop?.leaderboard_2 as Advertisement,
          leaderboard_1: adverts?.desktop?.leaderboard_1 as Advertisement,
          layer: adverts?.desktop?.layer as Advertisement,
          interstitial: adverts?.desktop?.interstitial as Advertisement,
          interstitialMobile: adverts?.mobile?.interstitial as Advertisement,
        });

        if (this.route.snapshot.firstChild?.data?.['isLeaderboardAdHidden']) {
          this.ads.set({});
          this.adverts = undefined;
        }

        this.areAdsInitiated = true;
      });

    this.apiService
      .getPortfolioFooter()
      .pipe(take(1))
      .subscribe((data: PortfolioResponse) => {
        this.mediaworksFooter.set(data.data);
      });

    const responseData: InitResolverData = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews.set(responseData?.init?.breakingNews);

    const {
      menu: { header, header_2, footer },
    } = responseData || {};
    this.mainMenu.set(header ?? []);
    this.topMenu.set(header_2 ?? []);
    this.footer.set(footer ?? []);
  }

  public ngAfterViewInit(): void {
    this.adblockerActiveStatus();
  }

  private baseElementPageTypeSwitch(path: string): {
    page: string;
    ads: AdvertisementBannerName[];
  } {
    const ads = [...ALL_BANNER_LIST, 'background'] as AdvertisementBannerName[];

    switch (path) {
      case '':
        return {
          page: PAGE_TYPES.main_page,
          ads,
        };
      default:
        return {
          page: this.adStoreAdo.articleParentCategory$.getValue(),
          ads,
        };
    }
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.areAdsInitiated = false;
  }

  private adblockerActiveStatus(): boolean | void {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the ado does not exist on SSR.
      return;
    }
    return this.isAdblockerActive.set(typeof this.window?.ado !== 'object');
  }
}
