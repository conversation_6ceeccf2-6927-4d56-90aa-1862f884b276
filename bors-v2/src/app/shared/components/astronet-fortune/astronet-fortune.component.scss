@use 'shared' as *;
:host {
  container-type: inline-size;
  position: relative;
  background-color: #4b3575;
  padding: 32px;
  display: block;
  width: 100%;
  clip-path: polygon(0 20px, 101% 0, 100% 100%, 0 100%);
  padding-top: 52px;
  @include media-breakpoint-down(sm) {
    padding: 16px;
    padding-top: 32px;
  }
  .astronet-fortune-header {
    display: flex;
    flex-wrap: wrap;
    @container (max-width: 420px) {
      &-left {
        font-size: 28px !important;
        line-height: 28px !important;
      }
      &-logo {
        width: 98px !important;
      }
    }

    &-left {
      display: flex;
      align-items: center;
      gap: 16px;
      color: var(--kui-white);
      font-size: 48px;
      font-style: normal;
      font-weight: 800;
      line-height: 42px; /* 87.5% */
      text-transform: uppercase;
      margin-bottom: 18px;
      @include media-breakpoint-down(sm) {
        font-size: 28px;
        line-height: 28px;
        margin-bottom: 16px;
        &-logo {
          width: 98px;
        }
      }
    }
    &-logo {
      width: 148px;
      aspect-ratio: 37/15;
    }
  }
  .astronet-fortune-card {
    display: flex;
    flex-direction: column;
    gap: 8px;
    &-swipe {
      position: static;
      ::ng-deep {
        .disabled {
          opacity: 0.5;
        }
        .bottom-navigation {
          position: absolute;
          right: 32px;
          top: 62px;
          color: var(--kui-white);
          fill: var(--kui-white);
          display: flex;
          gap: 8px;
          @include media-breakpoint-down(sm) {
            right: 16px;
            top: 33px;
          }
          .navigation-button kesma-icon {
            width: 40px;
            color: var(--kui-white);
          }
        }
      }
    }
    &-image {
      width: 100%;
      aspect-ratio: 4/3;
      object-fit: cover;
      clip-path: polygon(0 0, 101% 0, 100% 100%, 0 calc(100% - 20px));
    }
    &-name {
      color: var(--kui-white);
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 30px;
      text-transform: uppercase;
      @include media-breakpoint-down(sm) {
        font-size: 20px;
        line-height: 24px;
      }
    }
  }
  .astronet-fortune-more {
    margin-top: 10px;
    color: var(--kui-white);
    fill: var(--kui-white);
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 125% */
    display: flex;
    gap: 10px;
    @include media-breakpoint-down(sm) {
      margin-top: 16px;
    }
  }
}
