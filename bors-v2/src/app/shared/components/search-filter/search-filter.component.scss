@use 'shared' as *;

.search-filter {
  &-bar {
    display: flex;
    color: var(--kui-black-950);
    width: 100%;
    height: 60px;
    margin-bottom: 40px;
    background-color: var(--kui-gray-250);
    align-items: center;
    padding-left: 20px;

    @include media-breakpoint-down(md) {
      height: 44px;
      margin-bottom: 32px;
    }

    &-input {
      padding: 0;
      background-color: var(--kui-gray-250);
      width: 100%;
      font-size: 16px;
      line-height: 44px;
      font-weight: 400;
      height: 44px;

      &::placeholder {
        color: #002234;
      }
    }

    &-clear {
      color: var(--kui-black-950);
      padding: 0 20px;

      kesma-icon {
        height: 10px;
        width: 10px;
      }
    }

    &-submit {
      line-height: 28px;
      font-weight: 700;
      background-color: var(--kui-red-500);
      color: var(--kui-white);
      font-size: 16px;
      padding-inline: 20px;
      height: 60px;
      width: 100%;
      max-width: 98px;

      &:hover {
        background-color: var(--kui-red-600);
      }

      @include media-breakpoint-down(md) {
        height: 44px;
      }
    }
  }

  &-select-wrapper {
    display: flex;
    width: 100%;
    gap: 32px;
    margin-bottom: 32px;

    @include media-breakpoint-down(md) {
      flex-direction: column;
      gap: 16px;
    }

    app-search-select ::ng-deep {
      flex: 1;

      ng-select {
        .ng-select {
          &-container {
            height: 40px;
          }
        }
      }
    }

    button.tag-filter-toggle {
      flex: 1;
      color: var(--kui-red-500);
      border: 2px solid var(--kui-red-500);
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      height: 40px;
      min-height: 40px;
      cursor: pointer;
      box-sizing: border-box;

      &:hover {
        color: var(--kui-red-700);
        border: 2px solid var(--kui-red-700);
      }

      &.active {
        background-color: var(--kui-red-500);
        color: var(--kui-white);
      }
    }
  }
}
