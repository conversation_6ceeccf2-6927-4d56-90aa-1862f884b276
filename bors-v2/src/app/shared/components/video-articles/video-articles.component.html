<div class="wrapper">
  <div class="header">
    <h2 class="title">VIDEÓK</h2>
    <a class="link" routerLink="/kereses" [queryParams]="{ 'content_types[]': 'articleVideo' }">
      <PERSON><PERSON><PERSON>
      <kesma-icon name="arrow-right-long" [size]="14" [height]="16"></kesma-icon>
    </a>
  </div>
  @if (data?.length) {
    <div class="articles">
      @for (article of data; track article.id) {
        <app-article-card class="article-card" [data]="article" [hasBlockBackground]="true" [styleId]="ArticleCardType.TopImgTagTitle"></app-article-card>
      }
    </div>
  }
</div>
