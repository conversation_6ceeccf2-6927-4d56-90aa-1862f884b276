<div class="dossier">
  @switch (styleId()) {
    @case (DossierCardType.Layout) {
      <div class="dossier-header">
        <h2 class="dossier-header-title">
          <kesma-icon class="dossier-icon" name="dossier" [size]="41" [height]="27" />
          Dosszié
        </h2>
        <a [routerLink]="['/dosszie', data?.slug]" class="dossier-link">{{ moreButtonLabel }}</a>
      </div>
      @if (sponsoredData()?.sponsorship) {
        <app-dossier-sponsoration-header [data]="sponsoredData()"></app-dossier-sponsoration-header>
      }
      <div class="dossier-card" [class.sponsored]="sponsoredData()?.sponsorship">
        <a class="dossier-thumbnail-box" [routerLink]="['/dosszie', data?.slug]">
          <img
            class="dossier-thumbnail"
            [class.is-placeholder]="!data?.headerImage"
            [src]="data?.headerImage || PlaceholderImg"
            [alt]="data?.title"
            loading="lazy"
          />
        </a>
        <div class="dossier-details">
          <a [routerLink]="['/dosszie', data?.slug]">
            <h2 class="dossier-title">{{ data?.title }}</h2>
          </a>
          <div class="dossier-articles">
            @if (data?.mainArticle; as article) {
              @if (article?.title) {
                <div class="divider"></div>
                <a class="article-link" [routerLink]="buildArticleUrl(article)">
                  <kesma-icon name="plus" class="plus" [size]="12" />
                  <h2 class="article-title">{{ article.title }}</h2>
                </a>
              }
            }
            @for (article of data?.secondaryArticles; track article.slug) {
              <div class="divider"></div>
              <a class="article-link" [routerLink]="buildArticleUrl(article)">
                <kesma-icon name="plus" class="plus" [size]="12" />
                <h2 class="article-title">{{ article.title }}</h2>
              </a>
            }
            <div class="divider"></div>
          </div>
        </div>
      </div>
    }
    @case (DossierCardType.Article) {
      <div class="dossier-header">
        <h2 class="dossier-header-title">
          <kesma-icon class="dossier-icon" name="dossier" [size]="24" [height]="16" />
          Dosszié
        </h2>
        <a class="dossier-header-link" [routerLink]="['/dosszie', data?.slug]">
          Tovább a dossziéra
          <kesma-icon class="arrow-right" name="arrow-right-long" [size]="16" />
        </a>
      </div>
      <div class="dossier-articles">
        @if (data?.mainArticle; as article) {
          <app-article-card [data]="article" [styleId]="ArticleCardType.ArticleRecommendationNoImg"></app-article-card>
        }
        @for (article of data?.secondaryArticles; track article.slug) {
          <app-article-card [data]="article" [styleId]="ArticleCardType.ArticleRecommendationNoImg"></app-article-card>
        }
      </div>
    }
  }
</div>
