@use 'shared' as *;

:host {
  --clip-path-height: 30px;

  display: block;
  container-type: inline-size;
  color: var(--kui-black-950);
  .minute-by-minute {
    display: flex;
    flex-direction: column;
    gap: 32px;
    @include container-breakpoint-down(sm) {
      gap: 8px;
    }
  }
  .header {
    display: flex;
    border-block: 1px solid var(--kui-gray-200);
    padding-block: 8px;
    gap: 16px;
    @include container-breakpoint-down(sm) {
      gap: 8px;
      app-page-title::ng-deep .page-title {
        font-size: 26px;
        line-height: 28px;
      }
    }
  }
  .body {
    display: flex;
    gap: 32px;
    @include container-breakpoint-down(sm) {
      flex-direction: column-reverse;
    }
  }
  .thumbnail {
    width: 100%;
    object-fit: cover;
    clip-path: polygon(0 0, 100% 0, 101% 50%, 100% calc(100% - #{var(--clip-path-height)}), 0 100%, 0 100%);
    @include container-breakpoint-down(sm) {
      --clip-path-height: 10px;
    }
    &-wrapper {
      width: 100%;
      display: block;
    }
  }
  .article {
    max-width: 417px;
    width: 100%;
    @include container-breakpoint-down(sm) {
      max-width: 100%;
    }
  }
  .mbm {
    display: flex;
    align-items: center;
    gap: 16px;
    @include container-breakpoint-down(sm) {
      &-icon {
        width: 24px;
      }
    }
    &-date {
      padding-inline: 7px;
      background-color: var(--kui-red-500);
      color: var(--kui-white);
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      height: 24px;
      display: flex;
      align-items: center;
    }
    &-title {
      font-size: 18px;
      line-height: 24px;
      font-weight: 700;
      &:hover {
        color: var(--kui-black-1200);
      }
      @include container-breakpoint-down(sm) {
        font-size: 14px;
        line-height: 16px;
      }
    }
  }
  .divider {
    width: 100%;
    height: 1px;
    background-color: var(--kui-gray-200);
    margin-block: 16px;
  }
  app-article-card {
    margin-bottom: 0;
  }
}
