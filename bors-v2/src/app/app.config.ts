import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import { routes } from './app.routes';
import { provideClientHydration } from '@angular/platform-browser';
import { AppEnvironment, provideEncodedTransferState } from '@trendency/kesma-core';
import { provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { GoogleTagManagerModule } from 'angular-google-tag-manager';
import { environment } from '../environments/environment';
import { provideAnimations } from '@angular/platform-browser/animations';
import { authInterceptor, portalHeaderHttpInterceptor } from './shared';
import { DateFnsConfigurationService } from 'ngx-date-fns';
import { hu } from 'date-fns/locale';
import { register as registerSwiperElement } from 'swiper/element/bundle';
import { adDebugFactory, DEV_AD_DEBUG } from '@trendency/kesma-ui';

const GTAG_PROVIDER = [{ provide: 'googleTagManagerId', useValue: environment.googleTagManager }];

const huConfig = new DateFnsConfigurationService();
huConfig.setLocale(hu);

registerSwiperElement();

export const appConfig: ApplicationConfig = {
  providers: [
    provideEncodedTransferState(),
    provideAnimations(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withEnabledBlockingInitialNavigation(), withInMemoryScrolling({ scrollPositionRestoration: 'enabled', anchorScrolling: 'enabled' })),
    provideClientHydration(),
    provideHttpClient(withInterceptorsFromDi(), withInterceptors([portalHeaderHttpInterceptor, authInterceptor])),
    importProvidersFrom(GoogleTagManagerModule),
    {
      provide: AppEnvironment,
      useValue: environment,
    },
    {
      provide: DateFnsConfigurationService,
      useValue: huConfig,
    },
    {
      provide: DEV_AD_DEBUG,
      useFactory: adDebugFactory,
      deps: [AppEnvironment],
    },
    ...GTAG_PROVIDER,
  ],
};
