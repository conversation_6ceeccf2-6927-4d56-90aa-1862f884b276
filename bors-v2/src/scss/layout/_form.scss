.bors-form {
  &-checkbox {
    display: inline-flex;
    cursor: pointer;
    padding-left: 36px;
    flex-wrap: wrap;

    input {
      display: none;
    }

    &.centered {
      align-items: center;

      input:checked {
        & + span:before {
          top: -4px;
        }
      }
    }

    &:before {
      content: '';
      display: block;
      width: 24px;
      height: 24px;
      background-image: url(/assets/images/icons/form-checkbox-unchecked.svg);
      position: absolute;
      left: 0;
    }

    input:checked {
      & + span:before {
        content: '';
        display: block;
        width: 24px;
        height: 24px;
        position: absolute;
        left: 0;
        background-image: url(/assets/images/icons/form-checkbox-checked.svg);
      }
    }
  }
}

kesma-form-control {
  kesma-form-control-error .form-error {
    color: var(--kui-error) !important;
    top: 0 !important;
  }

  &.checkbox,
  &.radio {
    kesma-form-control-error .form-error {
      bottom: -16px !important;
      left: 40px !important;
      top: initial !important;
    }
  }
}
